{"permissions": {"allow": ["Bash(ls:*)", "<PERSON><PERSON>(../../openfoam.sh:*)", "<PERSON><PERSON>(docker:*)", "Bash(grep:*)", "Bash(timeout 60 docker run --rm --user=\"$(id -u):$(id -g)\" --volume=\"$(pwd):/home/<USER>\" opencfd/openfoam-run:latest interFoam)", "Bash(timeout 120 docker run --rm --user=\"$(id -u):$(id -g)\" --volume=\"$(pwd):/home/<USER>\" opencfd/openfoam-run:latest interFoam)", "Bash(cp:*)", "<PERSON><PERSON>(chmod:*)", "Bash(./setup_realistic_cases.sh:*)", "Bash(rm:*)", "<PERSON><PERSON>(../openfoam.sh -c \"blockMesh\")", "Bash(timeout 300 docker run --rm --user=\"$(id -u):$(id -g)\" --volume=\"$(pwd):/home/<USER>\" opencfd/openfoam-run:latest interFoam)", "Bash(timeout 300 docker run --rm -v \"$(pwd)\":/data -w /data openfoam/openfoam9-paraview56 interFoam)", "Bash(find:*)", "<PERSON><PERSON>(timeout:*)", "Bash(../openfoam.sh:*)", "Bash(for:*)", "Bash(do echo \"Copying 0/ directory to $case\")", "Bash(done)", "Bash(do echo \"=== Copying missing files to $case ===\")", "Bash(echo)", "Bash(do echo \"--- $case ---\")"], "deny": []}}