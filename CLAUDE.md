# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an OpenFOAM computational fluid dynamics study quantifying wave energy reduction effectiveness of 5 coral morphologies: BR01 (Branching), CY02 (Corymbose), EN03 (Encrusting), MA04 (Massive), and TB05 (Table). The study uses exact wave parameters and coral geometries scaled to 5cm height.

## OpenFOAM Environment

### Docker/Container Setup
- OpenFOAM runs in Docker containers using `openfoam.sh` script
- <PERSON><PERSON><PERSON> handles user mapping, X11 forwarding, and volume mounting
- Usage: `./openfoam.sh [options] [-- application ...]`
- Default image: `opencfd/openfoam-run:latest`

### Running OpenFOAM Commands
All OpenFOAM commands must be executed within the container:
```bash
# Enter container environment
./openfoam.sh

# Or run single commands
./openfoam.sh -- blockMesh
./openfoam.sh -- snappyHexMesh -overwrite
./openfoam.sh -- interFoam
```

## Case Structure and Workflow

### Case Directory Layout
Each coral case follows standard OpenFOAM structure:
- `0/` - Initial and boundary conditions (U, alpha.water, k, omega, p_rgh)
- `constant/` - Physical properties and geometry (waveProperties, transportProperties, triSurface/)
- `system/` - Solver settings and mesh controls (controlDict, snappyHexMeshDict, blockMeshDict)
- `mesh/` - Separate meshing environment with additional STL files

### Standard OpenFOAM Execution Sequence
Based on `case_info.json` run_sequence:
1. `blockMesh` - Generate background mesh
2. Copy coral STL to `constant/triSurface/coral.stl`
3. `setFields -default` - Initialize fields
4. `snappyHexMesh -overwrite` - Generate coral mesh
5. `setWaves` - Initialize wave field (waves2Foam)
6. `interFoam` - Run two-phase flow simulation

### Key Simulation Parameters
- Wave height: 0.16 m, Period: 1.0 s, Depth: 0.16 m
- Seawater density: 1023 kg/m³, Viscosity: 0.00097 Pa·s
- Time step: 0.01 s, Simulation time: 10 s (10 wave periods)
- Solver: interFoam (two-phase VOF)

## Critical Dependencies

### waves2Foam Library
- Required for wave generation and absorption
- Provides `setWaves` command and wave boundary conditions
- Configure wave parameters in `constant/waveProperties`
- Uses Stokes first-order wave theory with spatial relaxation zones

### Force Monitoring
- Forces calculated on coral patches using `forces` and `forceCoeffs` functions
- Output in `postProcessing/forces/` and `postProcessing/forceCoeffs/`
- Key metrics: drag coefficient (Cd), inertia coefficient (Cm), wave energy reduction

## Development Commands

### Case Preparation
```bash
# Navigate to case directory
cd cases/BR01_wave_case

# Check mesh quality
./openfoam.sh -- checkMesh

# Verify STL geometry
./openfoam.sh -- surfaceCheck constant/triSurface/coral.stl
```

### Simulation Execution
```bash
# Complete simulation sequence
./openfoam.sh -- blockMesh
./openfoam.sh -- setFields -default
./openfoam.sh -- snappyHexMesh -overwrite
./openfoam.sh -- setWaves
./openfoam.sh -- interFoam

# Monitor simulation progress
tail -f log.interFoam
```

### Parallel Execution
```bash
# Decompose for parallel run
./openfoam.sh -- decomposePar

# Run with 4 cores
./openfoam.sh -- mpirun -np 4 interFoam -parallel

# Reconstruct results
./openfoam.sh -- reconstructPar
```

### Quality Control
```bash
# Check residuals
grep "Solving for" log.interFoam | tail -10

# Monitor Courant number
grep "Courant Number" log.interFoam | tail -5

# Verify force calculations
tail postProcessing/forces/0/forces.dat
```

## Architecture Notes

### Mesh Generation Strategy
- snappyHexMesh with coral-specific refinement levels
- Boundary layer insertion (3 layers) for accurate flow near coral surfaces
- Feature edge refinement for complex coral geometries
- Quality controls: maxNonOrtho 65, maxInternalSkewness 4

### Wave Generation Configuration
- Inlet: Stokes first-order waves with spatial relaxation
- Outlet: Potential current with absorption zone
- Relaxation zones prevent wave reflection at boundaries
- Wave gauge monitoring at multiple locations

### Solver Configuration
- interFoam: Two-phase (water/air) incompressible flow
- PIMPLE algorithm for pressure-velocity coupling
- Adjustable time stepping with Courant number control (maxCo 0.5)
- k-omega SST turbulence model

## File Naming Conventions

- Case directories: `{CORAL_ID}_wave_case/` (e.g., BR01_wave_case)
- STL files: `{CORAL_ID}_*.stl` in geom/ directories
- Log files: `log.{application}` (e.g., log.interFoam, log.blockMesh)
- Results: Force data in postProcessing/forces/, field data in time directories

## Common Issues and Solutions

### Mesh Generation Failures
- Check STL file integrity with surfaceCheck
- Adjust refinement levels in snappyHexMeshDict
- Increase feature angle tolerance for complex geometries

### Solver Divergence
- Reduce time step (deltaT in controlDict)
- Increase relaxation factors in fvSolution
- Check initial conditions in 0/ directory

### Wave Generation Issues
- Verify waves2Foam installation with `setWaves -help`
- Check waveProperties syntax
- Ensure relaxation zones are properly sized (32 cells minimum)