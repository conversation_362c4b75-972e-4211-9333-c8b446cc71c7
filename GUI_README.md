# OpenFOAM Simulation Monitor GUI

A comprehensive Python GUI for monitoring OpenFOAM wave energy simulations in real-time.

## Features

### 🎛️ Control Panel
- **Case Selection**: Choose from 5 coral morphology cases (BR01, CY02, EN03, MA04, TB05)
- **Step-by-Step Execution**: Run individual simulation steps with visual status indicators
- **Automated Workflow**: Run all steps sequentially with one click
- **Status Monitoring**: Real-time status updates for each simulation step

### 📊 Real-time Monitoring
- **Progress Bar**: Visual simulation progress tracking
- **Live Log Display**: Real-time OpenFOAM output with timestamps
- **Log Management**: Save, clear, and auto-scroll log functionality
- **Process Control**: Start, stop, and monitor simulation processes

### 📈 Residuals Analysis
- **Convergence Plots**: Real-time residual plotting for p_rgh, Ux, Uy, Uz
- **Interactive Charts**: Zoom, pan, and save residual plots
- **Convergence Monitoring**: Track simulation convergence in real-time

### 📋 Results Analysis
- **Force Analysis**: Automatic force coefficient calculation
- **Report Generation**: Comprehensive simulation reports
- **ParaView Integration**: Direct launch to visualization
- **Data Export**: Export results in various formats

## Installation & Usage

### Quick Start
```bash
# From the wave_energy_study directory
python3 launch_monitor.py
```

The launcher will automatically:
1. Check for required dependencies
2. Install missing packages if needed
3. Launch the monitoring GUI

### Manual Installation
```bash
# Install dependencies
pip install -r requirements_gui.txt

# Launch GUI directly
python3 utilities/python/openfoam_monitor_gui.py
```

### System Requirements
- Python 3.7+
- tkinter (usually built-in)
- matplotlib
- numpy
- OpenFOAM with Docker support
- X11 forwarding (for Linux/WSL)

## Using the GUI

### 1. Select Case
- Choose your coral morphology case from the dropdown
- Cases: BR01 (Branching), CY02 (Corymbose), EN03 (Encrusting), MA04 (Massive), TB05 (Table)

### 2. Run Simulation
**Option A: Step-by-Step**
1. Click individual step buttons in order:
   - Clean Previous Data
   - Generate Base Mesh
   - Copy Coral Geometry
   - Initialize Fields
   - Generate Coral Mesh
   - Check Mesh Quality
   - Run Simulation

**Option B: Automated**
- Click "Run All Steps" to execute the complete workflow

### 3. Monitor Progress
- Switch to "Real-time Monitor" tab to watch simulation progress
- View live log output and progress bar
- Monitor residual convergence in "Residuals" tab

### 4. Analyze Results
- Use "Results" tab for post-processing
- Generate force analysis reports
- Launch ParaView for visualization

## GUI Tabs Overview

### Control Panel
- Case selection and simulation control
- Step-by-step execution with status indicators
- Start/stop controls and case information

### Real-time Monitor  
- Live progress tracking with progress bar
- Real-time log display with timestamps
- Log management (save, clear, auto-scroll)

### Residuals
- Interactive convergence plots
- Real-time residual monitoring
- Export and save plotting capabilities

### Results
- Simulation summary and analysis
- Force coefficient calculations
- Report generation and ParaView integration

## Simulation Steps Explained

1. **Clean Previous Data**: Removes old simulation files and directories
2. **Generate Base Mesh**: Creates background mesh using blockMesh
3. **Copy Coral Geometry**: Copies STL file to triSurface directory
4. **Initialize Fields**: Sets up initial flow conditions
5. **Generate Coral Mesh**: Creates refined mesh around coral using snappyHexMesh  
6. **Check Mesh Quality**: Validates mesh quality parameters
7. **Run Simulation**: Executes interFoam solver for wave-coral interaction

## Status Indicators

- 🔵 **Gray**: Step not started
- 🟠 **Orange**: Step in progress  
- 🟢 **Green**: Step completed successfully
- 🔴 **Red**: Step failed or error occurred

## Troubleshooting

### Common Issues

**GUI won't start**
- Check Python version: `python3 --version`
- Install dependencies: `pip install matplotlib numpy`
- Ensure tkinter is available: `python3 -c "import tkinter"`

**OpenFOAM commands fail**
- Verify openfoam.sh script exists and is executable
- Check Docker installation and permissions
- Ensure case directory structure is correct

**No residual data**
- Make sure simulation is running (interFoam step)
- Check that log parsing is working correctly
- Verify OpenFOAM is generating residual output

### File Structure Requirements
```
wave_energy_study/
├── openfoam.sh           # OpenFOAM Docker launcher
├── launch_monitor.py     # GUI launcher
├── cases/                # Simulation cases
│   ├── BR01_wave_case/   
│   ├── CY02_wave_case/
│   └── ...
└── utilities/python/
    └── openfoam_monitor_gui.py
```

## Tips for Best Results

1. **Run steps in order**: Some steps depend on previous ones
2. **Monitor logs**: Watch for errors or warnings in real-time
3. **Check mesh quality**: Poor mesh quality affects simulation stability
4. **Use parallel processing**: Decompose mesh for faster simulation
5. **Save logs**: Keep records of successful simulation parameters

## Advanced Features

- **Process monitoring**: Track CPU/memory usage during simulation
- **Custom scripts**: Integration with analysis Python scripts  
- **Batch processing**: Queue multiple cases for sequential processing
- **Remote monitoring**: Monitor simulations on remote servers
- **Export integration**: Direct export to analysis tools

For technical support or feature requests, refer to the main project documentation.