# 🌊 Coral Reef Geometry Preparation Guide

## 📁 Current Directory Structure
```
wave_energy_study/
├── cases/
│   └── {CORAL_ID}_wave_case/
│       ├── geom/                    # Master geometry files
│       │   ├── {CORAL_ID}_*.stl     # Master watertight coral geometry
│       │   └── *.step              # CAD source files
│       └── mesh/                    # SnappyHexMesh working directory
│           ├── constant/triSurface/ # Individual STL components
│           │   ├── Coral.stl        # Coral geometry
│           │   ├── Ground.stl       # Seabed/ground
│           │   ├── Inlet.stl        # Wave inlet boundary
│           │   ├── Outlet.stl       # Wave outlet boundary
│           │   └── Wall.stl         # Side walls
│           └── system/
│               ├── snappyHexMeshDict # Mesh generation settings
│               ├── blockMeshDict    # Background mesh
│               └── surfaceFeaturesDict
```

## 🎯 Recommended Geometry Preparation Workflow

### Phase 1: Master Geometry Creation (in Blender)

#### 1.1 Setup Blender Environment
```bash
# Install SnappyHexMesh GUI add-on for Blender
# Download from: https://github.com/tkeskita/snappyhexmesh_gui
```

#### 1.2 Create Master Watertight Geometry
1. **Import/Create Coral Geometry**
   - Import your coral model (STL, OBJ, or create from scratch)
   - Ensure watertight mesh (no holes, non-manifold edges)
   - Scale appropriately (typical: 5cm height for coral)

2. **Create Domain Boundaries**
   - **Ground**: Flat seabed surface (1.3m × 0.2m × 0.002m)
   - **Inlet**: Wave generation boundary (-0.2m from coral center)
   - **Outlet**: Wave absorption boundary (+1.1m from coral center)
   - **Walls**: Side boundaries (±0.1m from centerline)

3. **Verify Geometry Quality**
   - Check for watertight surfaces
   - Ensure proper scaling (coral ~0.05m height)
   - Verify boundary alignment

#### 1.3 Export Master Geometry
```python
# Save master file as: {CORAL_ID}CoralMesh.blend
# Example: BranchingCoralMesh.blend
```

### Phase 2: Component Extraction (SnappyHexMesh GUI)

#### 2.1 Configure SnappyHexMesh GUI Add-on
1. **Enable Add-on** in Blender preferences
2. **Set Export Directory**: `cases/{CORAL_ID}_wave_case/mesh/`
3. **Configure Mesh Parameters**:
   - Background mesh: 15×4×5 cells (for low performance)
   - Refinement levels: 0-2 depending on complexity
   - Feature angle: 30° (for coral features)

#### 2.2 Select and Export Components
**Selection Strategy:**
1. **Coral**: Select coral geometry only
   - Export as: `Coral.stl`
   - Refinement level: 1-2 (depending on complexity)

2. **Ground**: Select seabed surface
   - Export as: `Ground.stl`
   - Refinement level: 0

3. **Inlet**: Select wave inlet boundary
   - Export as: `Inlet.stl`
   - Refinement level: 0

4. **Outlet**: Select wave outlet boundary
   - Export as: `Outlet.stl`
   - Refinement level: 0

5. **Wall**: Select side walls (can combine front/back/bottom)
   - Export as: `Wall.stl`
   - Refinement level: 0

#### 2.3 Export Configuration Files
The SnappyHexMesh GUI will automatically generate:
- `snappyHexMeshDict`
- `blockMeshDict`
- `surfaceFeaturesDict`
- `run` script

### Phase 3: Geometry Validation

#### 3.1 Check STL Files
```bash
# Navigate to mesh directory
cd cases/{CORAL_ID}_wave_case/mesh/

# Check STL file integrity
surfaceCheck constant/triSurface/Coral.stl
surfaceCheck constant/triSurface/Ground.stl
```

#### 3.2 Verify Geometry Bounds
```python
# Use provided analysis script
python ../analyze_stl.py
```

Expected bounds for 5cm coral:
- **Coral**: Height ~0.05m, centered around origin
- **Ground**: Z-level ~0.002-0.005m
- **Domain**: X: -0.2 to 1.1m, Y: -0.1 to 0.1m, Z: 0 to 0.3m

### Phase 4: Mesh Generation

#### 4.1 Generate Background Mesh
```bash
cd cases/{CORAL_ID}_wave_case/mesh/
blockMesh
```

#### 4.2 Extract Surface Features
```bash
surfaceFeatureExtract
```

#### 4.3 Generate Snappy Mesh
```bash
snappyHexMesh -overwrite
```

#### 4.4 Check Mesh Quality
```bash
checkMesh -latestTime
```

## 🔧 Optimization Guidelines

### Coral Complexity Levels

#### Low Complexity (EN03 - Encrusting)
- Simple, flat coral structure
- Minimal surface features
- Refinement level: 0-1
- Mesh cells: ~3,000-5,000

#### Medium Complexity (MA04, TB05)
- Moderate 3D structure
- Some surface detail
- Refinement level: 1
- Mesh cells: ~8,000-12,000

#### High Complexity (CY02, BR01)
- Complex branching structure
- High surface detail
- Refinement level: 1-2
- Mesh cells: ~15,000-25,000

### Performance Optimization

#### For Low-Speed Computers:
1. **Reduce Background Mesh**: 10×3×4 cells
2. **Lower Refinement**: Max level 1
3. **Simplify Geometry**: Remove small features
4. **Increase Feature Angle**: 45° instead of 30°

#### For Better Accuracy:
1. **Increase Background Mesh**: 20×6×8 cells
2. **Higher Refinement**: Level 2-3 for coral
3. **Preserve Features**: Keep detailed geometry
4. **Stricter Feature Angle**: 15-20°

## 🚨 Common Issues & Solutions

### Geometry Issues
- **Non-watertight mesh**: Use Blender's mesh repair tools
- **Scale problems**: Verify coral height ~0.05m
- **Boundary misalignment**: Check domain coordinates

### Mesh Generation Issues
- **SnappyHexMesh fails**: Reduce refinement levels
- **Poor mesh quality**: Increase feature angle tolerance
- **Memory issues**: Reduce background mesh resolution

### Export Issues
- **Missing STL files**: Check Blender export settings
- **Wrong file paths**: Verify export directory
- **Corrupted files**: Re-export with ASCII format

## 📊 Quality Metrics

### Geometry Quality Checks
- **Watertight**: No holes or non-manifold edges
- **Proper scaling**: Coral height 0.04-0.06m
- **Boundary alignment**: Inlet/outlet perpendicular to flow
- **Surface area**: Coral area 0.01-0.04 m²

### Mesh Quality Targets
- **Aspect ratio**: < 10
- **Skewness**: < 0.8
- **Non-orthogonality**: < 70°
- **Cell count**: 3,000-25,000 (depending on performance level)

## 🎯 Best Practices

1. **Start Simple**: Begin with encrusting coral (EN03)
2. **Iterative Refinement**: Gradually increase complexity
3. **Validate Each Step**: Check geometry before meshing
4. **Document Changes**: Keep notes on modifications
5. **Backup Files**: Save working .blend files
6. **Test Mesh**: Run checkMesh before simulation

This workflow ensures consistent, high-quality geometry preparation for your coral reef wave energy simulations while maintaining compatibility with your existing SnappyHexMesh GUI workflow.

## 🤖 Automation Tools

### Geometry Validation Script
```bash
# Validate existing geometry
python geometry_validator.py cases/BR01_wave_case

# This will check:
# - STL file integrity
# - Geometry bounds and scaling
# - SnappyHexMesh configuration
# - Generate detailed report
```

### Blender Automation Helper
```python
# In Blender Text Editor, run:
exec(open("blender_geometry_helper.py").read())

# Then use:
quick_coral_setup()  # Complete automated setup
analyze_coral()      # Check current geometry
check_quality()      # Validate mesh quality
```

### Workflow Automation Script
```bash
# Complete workflow automation
python geometry_workflow.py --coral-type BR01 --create-new

# Or validate existing case
python geometry_workflow.py --coral-type BR01 --validate-only
```

## 📊 Expected Results by Coral Type

| Coral Type | Complexity | Mesh Cells | Refinement | Export Time |
|------------|------------|------------|------------|-------------|
| EN03       | Low        | 3,000-5,000| Level 0-1  | 2-3 min     |
| MA04       | Medium     | 8,000-12,000| Level 1   | 5-8 min     |
| TB05       | Medium     | 8,000-12,000| Level 1   | 5-8 min     |
| CY02       | High       | 15,000-20,000| Level 1-2| 10-15 min   |
| BR01       | High       | 20,000-25,000| Level 1-2| 15-20 min   |

## 🔄 Iterative Improvement Process

1. **Start Simple**: Begin with EN03 (encrusting coral)
2. **Validate Each Step**: Use geometry_validator.py after each export
3. **Gradual Complexity**: Move to higher complexity corals
4. **Performance Tuning**: Adjust refinement based on computer capabilities
5. **Quality Assurance**: Always run checkMesh before simulation

## 📞 Support & Troubleshooting

### Common Error Messages
- **"Non-manifold edges detected"**: Use Blender's mesh repair tools
- **"SnappyHexMesh failed"**: Reduce refinement levels
- **"Coral height incorrect"**: Use scale_coral_to_target() function
- **"Domain bounds invalid"**: Check boundary positioning

### Getting Help
1. Run geometry_validator.py for detailed diagnostics
2. Check log files in mesh/ directory
3. Use Blender's mesh analysis tools
4. Consult OpenFOAM documentation for SnappyHexMesh

This comprehensive workflow provides both manual control and automation options to suit your expertise level and project requirements.
