# Coral Morphology Hydrodynamics - Wave Energy Study

A comprehensive OpenFOAM-based study quantifying wave energy reduction effectiveness across 5 coral morphologies using realistic wave parameters and scaled coral geometries.

## Project Structure (Restructured)

```
wave_energy_study/
├── cases/                          # Individual coral case directories
│   ├── BR01_wave_case/            # Branching coral
│   ├── CY02_wave_case/            # Corymbose coral  
│   ├── EN03_wave_case/            # Encrusting coral
│   ├── MA04_wave_case/            # Massive coral
│   └── TB05_wave_case/            # Table coral
├── templates/                      # OpenFOAM configuration templates
│   ├── controlDict.template
│   ├── waveProperties.template
│   └── transportProperties.template
├── utilities/                      # Shared utilities and tools
│   ├── python/                    # Python analysis scripts
│   │   ├── coral_analysis.py      # Unified analysis framework
│   │   ├── legacy_wave_energy_analyzer.py
│   │   ├── longitudinal_contours.py
│   │   ├── create_contour_plots.py
│   │   ├── analyze_contour_data.py
│   │   ├── simple_side_view.py
│   │   └── minimal_side_view.py
│   └── openfoam/                  # OpenFOAM utilities
├── scripts/                       # Management and automation scripts
│   └── coral_manager.sh          # Unified case management
├── docs/                          # Documentation
└── README.md                      # This file
```

## Wave Parameters

- **Wave Height**: 0.16 m
- **Wave Period**: 1.0 s  
- **Water Depth**: 0.16 m
- **Wave Type**: Stokes first-order
- **Simulation Time**: 10 seconds (10 wave periods)

## Physical Properties

- **Seawater Density**: 1023 kg/m³
- **Seawater Viscosity**: 9.48×10⁻⁷ m²/s
- **Air Density**: 1.225 kg/m³
- **Surface Tension**: 0.0728 N/m

## Quick Start

### 1. Using the Unified Management Script

The `coral_manager.sh` script provides centralized control over all operations:

```bash
# Make script executable (if needed)
chmod +x scripts/coral_manager.sh

# List available cases
./scripts/coral_manager.sh list

# Setup all cases with templates
./scripts/coral_manager.sh batch-setup

# Check case status
./scripts/coral_manager.sh batch-status

# Generate mesh for specific case
./scripts/coral_manager.sh mesh BR01_wave_case

# Run simulation for specific case  
./scripts/coral_manager.sh simulate BR01_wave_case

# Run unified analysis
./scripts/coral_manager.sh analyze
```

### 2. Manual Operations

#### Setup Single Case
```bash
cd cases/BR01_wave_case
# Ensure coral STL is in constant/triSurface/coral.stl
../../openfoam.sh -- blockMesh
../../openfoam.sh -- setFields -default
../../openfoam.sh -- snappyHexMesh -overwrite
../../openfoam.sh -- setWaves
../../openfoam.sh -- interFoam
```

#### Run Analysis
```bash
python3 utilities/python/coral_analysis.py
```
## 🔧 Detailed Instructions

### A. Coral Geometry Preparation

The coral geometries need to be processed to create fluid domains suitable for CFD:

1. **Scale corals to realistic size (5cm height)**
2. **Create watertight enclosures around each coral**
3. **Subtract coral bodies from domains**
4. **Export clean fluid domains**

```bash
# Process single coral
python scripts/geometry_processing/coral_domain_generator.py --mode single --coral BR01

# Process all corals
python scripts/geometry_processing/coral_domain_generator.py --mode prepare_all

# Check processing results
python scripts/geometry_processing/coral_domain_generator.py --mode summary
```

**Expected Output:**
- `morphologies/fluid_domains/BR01_fluid_domain.stl` (and similar for other corals)
- `morphologies/fluid_domains/BR01_patches/` (boundary patch STL files)
- `morphologies/fluid_domains/processing_report.json`

### B. Case Generation

Generate complete OpenFOAM cases with exact wave parameters:

```bash
# Preview what will be generated (no files created)
python wave_energy_study/coral_wave_case_generator.py --mode preview

# Generate single case
python wave_energy_study/coral_wave_case_generator.py --mode single --coral BR01

# Generate all cases
python wave_energy_study/coral_wave_case_generator.py --mode setup_all
```

**Generated for each coral:**
- Complete OpenFOAM case directory
- Adapted mesh parameters based on coral complexity
- Wave generation with waves2Foam
- Force monitoring configuration
- Wave gauge setup for energy calculation

### C. Running Simulations

Each case requires the following OpenFOAM sequence:

```bash
# Navigate to case directory
cd wave_energy_study/cases/BR01_wave_case

# Generate background mesh
blockMesh

# Initialize fields
setFields -default

# Generate coral mesh
snappyHexMesh -overwrite

# Initialize wave field
setWaves

# Run simulation
interFoam > log.interFoam 2>&1 &

# Alternative: Run with Docker directly
cd BR01_wave_case && echo "BR01 interFoam starting (simplified)..." && \
docker run --rm -v "$(pwd)":/case opencfd/openfoam-run:latest bash -c "cd /case && interFoam" 2>&1 | tee log.interFoam

# Monitor progress
tail -f log.interFoam```

### D. Automated Execution Scripts

Create these shell scripts for automated execution:

#### `run_coral_simulation.sh`
```bash
#!/bin/bash
# Usage: ./run_coral_simulation.sh BR01

if [ $# -eq 0 ]; then
    echo "Usage: $0 <CORAL_ID>"
    echo "Available: BR01, CY02, EN03, MA04, TB05"
    exit 1
fi

CORAL_ID=$1
CASE_DIR="wave_energy_study/cases/${CORAL_ID}_wave_case"

if [ ! -d "$CASE_DIR" ]; then
    echo "Error: Case directory $CASE_DIR not found"
    exit 1
fi

echo "Starting simulation for $CORAL_ID..."
cd "$CASE_DIR"

# Execute OpenFOAM sequence
echo "Generating background mesh..."
blockMesh > log.blockMesh 2>&1

echo "Initializing fields..."
setFields -default > log.setFields 2>&1

echo "Generating coral mesh..."
snappyHexMesh -overwrite > log.snappyHexMesh 2>&1

echo "Setting up waves..."
setWaves > log.setWaves 2>&1

echo "Running interFoam simulation..."
interFoam > log.interFoam 2>&1

echo "Simulation completed for $CORAL_ID"
echo "Check log files in $CASE_DIR"
```

#### `run_all_cases.sh`
```bash
#!/bin/bash
# Run all coral simulations in parallel

CORAL_IDS=("BR01" "CY02" "EN03" "MA04" "TB05")

echo "Starting parallel execution of all coral simulations..."

for coral_id in "${CORAL_IDS[@]}"; do
    echo "Launching $coral_id simulation..."
    ./run_coral_simulation.sh "$coral_id" &
done

echo "All simulations launched. Use 'jobs' to monitor progress."
echo "Logs will be in wave_energy_study/cases/*/log.*"

# Wait for all background jobs to complete
wait
echo "All simulations completed!"
```

### E. Post-Processing and Analysis

Extract Wave Energy Reduction and other metrics:

```bash
# Analyze single case
python wave_energy_study/wave_energy_analyzer.py --case BR01_wave_case

# Batch analyze all cases
python wave_energy_study/wave_energy_analyzer.py --batch-analyze

# Generate report only (if analysis already done)
python wave_energy_study/wave_energy_analyzer.py --generate-report
```

**Output Files:**
- `results/wave_energy_summary.csv` - Summary table
- `results/batch_analysis_results.json` - Detailed results
- `results/wave_energy_reduction_report.md` - Comprehensive report
- `results/wave_energy_analysis_plots.png` - Visualization plots

## 📊 Expected Results

### Wave Energy Reduction Ranking (Predicted)
Based on morphological complexity and flow interaction:

1. **BR01 (Branching)**: 40-60% - Highest energy dissipation
2. **CY02 (Corymbose)**: 30-45% - High complexity
3. **TB05 (Table)**: 25-40% - Plate-induced turbulence  
4. **MA04 (Massive)**: 15-30% - Moderate interaction
5. **EN03 (Encrusting)**: 5-20% - Minimal interaction

### Key Metrics Extracted
- **Wave Energy Reduction (%)** - Primary metric
- **Drag Coefficient (Cd)** - Force characterization
- **Inertia Coefficient (Cm)** - Inertial effects
- **Transmission Coefficient (Kt)** - Wave transmission
- **Energy Loss (W/m)** - Power dissipation

## 🔍 Quality Control

### Pre-Simulation Checks
```bash
# Check mesh quality
checkMesh

# Verify boundary patches
surfaceCheck constant/triSurface/coral_domain.stl

# Validate wave setup
grep -i error log.*
```

### Simulation Status Monitoring
```bash
# Check residuals
grep "Solving for" log.interFoam | tail -10

# Monitor Courant number
grep "Courant Number" log.interFoam | tail -5

# Check force convergence
tail postProcessing/forces/0/forces.dat
```

### Troubleshooting

**Common Issues and Solutions:**

1. **Mesh Generation Fails**
   ```bash
   # Check STL file integrity
   surfaceCheck constant/triSurface/coral_domain.stl
   
   # Reduce mesh complexity in snappyHexMeshDict
   # Increase feature angle tolerance
   ```

2. **Solver Divergence**
   ```bash
   # Reduce time step
   # Increase relaxation factors in fvSolution
   # Check initial conditions in 0/ directory
   ```

3. **Wave Generation Issues**
   ```bash
   # Verify waves2Foam installation
   setWaves -help
   
   # Check waveProperties file syntax
   # Ensure relaxation zones are properly sized
   ```

## 🎯 Performance Optimization

### Parallel Execution
```bash
# Decompose case for parallel execution
decomposePar

# Run in parallel (4 cores)
mpirun -np 4 interFoam -parallel > log.interFoam 2>&1

# Reconstruct results
reconstructPar
```

### Mesh Resolution Guidelines
- **Minimum 10 cells per wave height**
- **5+ boundary layers for complex geometries**
- **Courant number < 0.5**

### Expected Runtime (24-core HPC)
- **BR01 (Branching)**: 8-12 hours
- **CY02 (Corymbose)**: 6-10 hours  
- **TB05 (Table)**: 6-8 hours
- **MA04 (Massive)**: 4-6 hours
- **EN03 (Encrusting)**: 3-5 hours

## 📚 Technical References

### Wave Energy Calculation
```
E = (1/8) × ρ × g × H²
Energy Reduction (%) = ((E_incident - E_transmitted) / E_incident) × 100
```

### Force Coefficients
```
Cd = F_drag / (0.5 × ρ × U² × A_ref)
Cm = F_inertia / (ρ × V_coral × du/dt)
```

### OpenFOAM Solver: interFoam
- Two-phase (water/air) volume-of-fluid solver
- PIMPLE algorithm for pressure-velocity coupling
- K-omega SST turbulence model

### waves2Foam Library
- Wave generation and absorption
- Stokes first-order wave theory
- Spatial relaxation zones

## 🚀 Getting Started Checklist

- [ ] Install trimesh: `pip install trimesh numpy scipy matplotlib pandas`
- [ ] Verify OpenFOAM installation: `blockMesh -help`
- [ ] Check waves2Foam: `setWaves -help`
- [ ] Generate coral domains: `python scripts/geometry_processing/coral_domain_generator.py --mode prepare_all`
- [ ] Create simulation cases: `python wave_energy_study/coral_wave_case_generator.py --mode setup_all`
- [ ] Preview cases: `python wave_energy_study/coral_wave_case_generator.py --mode preview`
- [ ] Create execution scripts: Copy shell scripts above to project root
- [ ] Run test case: `./run_coral_simulation.sh EN03` (fastest case)
- [ ] Monitor simulation: `tail -f wave_energy_study/cases/EN03_wave_case/log.interFoam`
- [ ] Analyze results: `python wave_energy_study/wave_energy_analyzer.py --case EN03_wave_case`

## 📞 Support

For questions or issues:
1. Check log files in case directories
2. Review morphometric analysis data in `morphologies/morphometric_analysis/`
3. Verify STL file integrity with surfaceCheck
4. Ensure waves2Foam is properly compiled and linked

---

**Study Objective**: Quantify wave energy reduction by coral morphologies for marine engineering applications and coastal protection design.

**Expected Outcome**: Ranking of coral types by wave energy reduction effectiveness with quantitative metrics for engineering design guidelines.