{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# 🌊 Comprehensive Wave Energy Simulation & Analysis\n",
    "## Coral Reef Wave Energy Reduction Study - Kaggle Edition\n",
    "\n",
    "This notebook provides a complete implementation of OpenFOAM wave energy simulations optimized for <PERSON><PERSON>'s environment. It includes:\n",
    "\n",
    "- 🚀 **Optimized OpenFOAM Setup** for cloud environments\n",
    "- 🏗️ **Automated Simulation Management** for 5 coral types\n",
    "- 📊 **Comprehensive Data Analysis** and visualization\n",
    "- ⚡ **Performance Optimization** for limited computational resources\n",
    "- 📈 **Interactive Results Dashboard**\n",
    "\n",
    "### Coral Types Studied:\n",
    "1. **BR01** - Branching Coral (High complexity)\n",
    "2. **CY02** - Corymbose Coral (High complexity) \n",
    "3. **TB05** - Table Coral (High complexity)\n",
    "4. **MA04** - Massive Coral (Medium complexity)\n",
    "5. **EN03** - Encrusting Coral (Low complexity)"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 📦 1. Environment Setup & Dependencies"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Install required packages\n",
    "!pip install numpy pandas matplotlib seaborn plotly ipywidgets tqdm\n",
    "!pip install vtk pyvista  # For 3D visualization\n",
    "!pip install scipy scikit-learn  # For advanced analysis\n",
    "\n",
    "# System packages for OpenFOAM\n",
    "!apt-get update -qq\n",
    "!apt-get install -y wget curl build-essential cmake git\n",
    "\n",
    "print(\"✅ Dependencies installed successfully!\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Import libraries\n",
    "import os\n",
    "import sys\n",
    "import time\n",
    "import json\n",
    "import shutil\n",
    "import subprocess\n",
    "import numpy as np\n",
    "import pandas as pd\n",
    "import matplotlib.pyplot as plt\n",
    "import seaborn as sns\n",
    "import plotly.graph_objects as go\n",
    "import plotly.express as px\n",
    "from plotly.subplots import make_subplots\n",
    "import ipywidgets as widgets\n",
    "from IPython.display import display, HTML, clear_output\n",
    "from tqdm.notebook import tqdm\n",
    "import warnings\n",
    "warnings.filterwarnings('ignore')\n",
    "\n",
    "# Set plotting style\n",
    "plt.style.use('seaborn-v0_8')\n",
    "sns.set_palette(\"husl\")\n",
    "\n",
    "print(\"📚 Libraries imported successfully!\")\n",
    "print(f\"🐍 Python version: {sys.version}\")\n",
    "print(f\"💻 Working directory: {os.getcwd()}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 🐳 2. OpenFOAM Installation & Setup"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Install OpenFOAM using Docker (recommended for Kaggle)\n",
    "def setup_openfoam():\n",
    "    \"\"\"Setup OpenFOAM environment for Kaggle\"\"\"\n",
    "    print(\"🐳 Setting up OpenFOAM environment...\")\n",
    "    \n",
    "    # Check if Docker is available\n",
    "    try:\n",
    "        result = subprocess.run(['docker', '--version'], capture_output=True, text=True)\n",
    "        if result.returncode == 0:\n",
    "            print(f\"✅ Docker available: {result.stdout.strip()}\")\n",
    "            \n",
    "            # Pull OpenFOAM Docker image\n",
    "            print(\"📥 Pulling OpenFOAM Docker image...\")\n",
    "            subprocess.run(['docker', 'pull', 'openfoam/openfoam8-paraview56'], check=True)\n",
    "            \n",
    "            return True\n",
    "    except:\n",
    "        print(\"⚠️ Docker not available, installing OpenFOAM directly...\")\n",
    "        \n",
    "        # Alternative: Install OpenFOAM directly\n",
    "        commands = [\n",
    "            \"curl -s https://dl.openfoam.org/gpg.key | apt-key add -\",\n",
    "            \"add-apt-repository http://dl.openfoam.org/ubuntu\",\n",
    "            \"apt-get update\",\n",
    "            \"apt-get install -y openfoam8\"\n",
    "        ]\n",
    "        \n",
    "        for cmd in commands:\n",
    "            subprocess.run(cmd.split(), check=True)\n",
    "        \n",
    "        return False\n",
    "\n",
    "# Setup OpenFOAM\n",
    "docker_available = setup_openfoam()\n",
    "print(\"🌊 OpenFOAM setup completed!\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 📁 3. Simulation Data & Case Setup"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Download simulation data from Kaggle dataset\n",
    "# Note: Upload your wave_energy_study folder as a Kaggle dataset first\n",
    "\n",
    "# If running on Kaggle, the dataset will be in /kaggle/input/\n",
    "# If running locally, adjust the path accordingly\n",
    "\n",
    "import zipfile\n",
    "from pathlib import Path\n",
    "\n",
    "def setup_simulation_data():\n",
    "    \"\"\"Setup simulation data and cases\"\"\"\n",
    "    print(\"📁 Setting up simulation data...\")\n",
    "    \n",
    "    # Check for Kaggle environment\n",
    "    if os.path.exists('/kaggle/input'):\n",
    "        input_path = '/kaggle/input/wave-energy-study'  # Adjust to your dataset name\n",
    "        if os.path.exists(input_path):\n",
    "            print(f\"✅ Found Kaggle dataset at: {input_path}\")\n",
    "            # Copy data to working directory\n",
    "            shutil.copytree(input_path, './wave_energy_study')\n",
    "        else:\n",
    "            print(\"⚠️ Kaggle dataset not found. Creating minimal setup...\")\n",
    "            create_minimal_setup()\n",
    "    else:\n",
    "        print(\"💻 Local environment detected. Using current directory...\")\n",
    "        if not os.path.exists('./cases'):\n",
    "            create_minimal_setup()\n",
    "    \n",
    "    # Change to simulation directory\n",
    "    if os.path.exists('./wave_energy_study'):\n",
    "        os.chdir('./wave_energy_study')\n",
    "    \n",
    "    print(f\"📍 Current directory: {os.getcwd()}\")\n",
    "    print(f\"📂 Available files: {os.listdir('.')}\")\n",
    "\n",
    "def create_minimal_setup():\n",
    "    \"\"\"Create minimal simulation setup if data not available\"\"\"\n",
    "    print(\"🏗️ Creating minimal simulation setup...\")\n",
    "    \n",
    "    # Create directory structure\n",
    "    os.makedirs('./wave_energy_study/cases', exist_ok=True)\n",
    "    os.makedirs('./wave_energy_study/scripts', exist_ok=True)\n",
    "    os.makedirs('./wave_energy_study/results', exist_ok=True)\n",
    "    \n",
    "    # Create coral case info\n",
    "    coral_cases = {\n",
    "        'BR01': {'name': 'Branching Coral', 'complexity': 'HIGH'},\n",
    "        'CY02': {'name': 'Corymbose Coral', 'complexity': 'HIGH'},\n",
    "        'TB05': {'name': 'Table Coral', 'complexity': 'HIGH'},\n",
    "        'MA04': {'name': 'Massive Coral', 'complexity': 'MEDIUM'},\n",
    "        'EN03': {'name': 'Encrusting Coral', 'complexity': 'LOW'}\n",
    "    }\n",
    "    \n",
    "    for case_id, info in coral_cases.items():\n",
    "        case_dir = f'./wave_energy_study/cases/{case_id}_wave_case'\n",
    "        os.makedirs(case_dir, exist_ok=True)\n",
    "        \n",
    "        # Create case info file\n",
    "        case_info = {\n",
    "            'coral_id': case_id,\n",
    "            'coral_name': info['name'],\n",
    "            'complexity': info['complexity'],\n",
    "            'wave_parameters': {\n",
    "                'height': '0.16 m',\n",
    "                'period': '1.0 s',\n",
    "                'depth': '0.16 m'\n",
    "            }\n",
    "        }\n",
    "        \n",
    "        with open(f'{case_dir}/case_info.json', 'w') as f:\n",
    "            json.dump(case_info, f, indent=2)\n",
    "\n",
    "# Setup simulation data\n",
    "setup_simulation_data()"
   ]
  }
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 🎛️ 4. Simulation Configuration & Controls"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Simulation configuration class\n",
    "class WaveSimulationConfig:\n",
    "    \"\"\"Configuration manager for wave simulations\"\"\"\n",
    "    \n",
    "    def __init__(self):\n",
    "        self.performance_levels = {\n",
    "            'ultra-low': {\n",
    "                'mesh': (20, 20, 8),\n",
    "                'cells': 3200,\n",
    "                'sim_time': 3.0,\n",
    "                'time_step': 0.02,\n",
    "                'description': 'Fastest - Basic accuracy (5-10 min)'\n",
    "            },\n",
    "            'low': {\n",
    "                'mesh': (25, 25, 8),\n",
    "                'cells': 5000,\n",
    "                'sim_time': 4.0,\n",
    "                'time_step': 0.015,\n",
    "                'description': 'Fast - Good accuracy (8-15 min)'\n",
    "            },\n",
    "            'medium': {\n",
    "                'mesh': (30, 30, 10),\n",
    "                'cells': 9000,\n",
    "                'sim_time': 5.0,\n",
    "                'time_step': 0.01,\n",
    "                'description': 'Balanced - High accuracy (15-25 min)'\n",
    "            },\n",
    "            'high': {\n",
    "                'mesh': (40, 40, 12),\n",
    "                'cells': 19200,\n",
    "                'sim_time': 8.0,\n",
    "                'time_step': 0.005,\n",
    "                'description': 'Slow - Very high accuracy (30-45 min)'\n",
    "            }\n",
    "        }\n",
    "        \n",
    "        self.coral_cases = {\n",
    "            'EN03': {'name': 'Encrusting Coral', 'complexity': 'LOW', 'color': '#2E8B57'},\n",
    "            'MA04': {'name': 'Massive Coral', 'complexity': 'MEDIUM', 'color': '#4682B4'},\n",
    "            'TB05': {'name': 'Table Coral', 'complexity': 'HIGH', 'color': '#DAA520'},\n",
    "            'CY02': {'name': 'Corymbose Coral', 'complexity': 'HIGH', 'color': '#CD853F'},\n",
    "            'BR01': {'name': 'Branching Coral', 'complexity': 'HIGH', 'color': '#8B4513'}\n",
    "        }\n",
    "        \n",
    "        self.wave_params = {\n",
    "            'height': 0.16,  # m\n",
    "            'period': 1.0,   # s\n",
    "            'depth': 0.16,   # m\n",
    "            'density': 1023, # kg/m³\n",
    "            'viscosity': 0.00097  # Pa·s\n",
    "        }\n",
    "    \n",
    "    def get_config_summary(self, performance_level):\n",
    "        \"\"\"Get configuration summary for display\"\"\"\n",
    "        config = self.performance_levels[performance_level]\n",
    "        return f\"\"\"\n",
    "        🎯 Performance Level: {performance_level.upper()}\n",
    "        📐 Mesh Resolution: {config['mesh'][0]}×{config['mesh'][1]}×{config['mesh'][2]} ({config['cells']:,} cells)\n",
    "        ⏱️ Simulation Time: {config['sim_time']} seconds\n",
    "        🔄 Time Step: {config['time_step']} seconds\n",
    "        📊 Description: {config['description']}\n",
    "        \"\"\"\n",
    "\n",
    "# Initialize configuration\n",
    "config = WaveSimulationConfig()\n",
    "print(\"⚙️ Simulation configuration initialized!\")\n",
    "print(f\"🌊 Wave parameters: H={config.wave_params['height']}m, T={config.wave_params['period']}s\")\n",
    "print(f\"🪸 Coral cases: {list(config.coral_cases.keys())}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Interactive simulation controls\n",
    "def create_simulation_controls():\n",
    "    \"\"\"Create interactive widgets for simulation control\"\"\"\n",
    "    \n",
    "    # Performance level selector\n",
    "    performance_widget = widgets.Dropdown(\n",
    "        options=list(config.performance_levels.keys()),\n",
    "        value='medium',\n",
    "        description='Performance:',\n",
    "        style={'description_width': 'initial'}\n",
    "    )\n",
    "    \n",
    "    # Coral case selector\n",
    "    coral_widget = widgets.SelectMultiple(\n",
    "        options=list(config.coral_cases.keys()),\n",
    "        value=['EN03'],  # Start with simplest\n",
    "        description='Coral Cases:',\n",
    "        style={'description_width': 'initial'}\n",
    "    )\n",
    "    \n",
    "    # Parallel processing\n",
    "    parallel_widget = widgets.Checkbox(\n",
    "        value=True,\n",
    "        description='Enable Parallel Processing',\n",
    "        style={'description_width': 'initial'}\n",
    "    )\n",
    "    \n",
    "    # Run button\n",
    "    run_button = widgets.Button(\n",
    "        description='🚀 Run Simulations',\n",
    "        button_style='success',\n",
    "        layout=widgets.Layout(width='200px', height='40px')\n",
    "    )\n",
    "    \n",
    "    # Output area\n",
    "    output = widgets.Output()\n",
    "    \n",
    "    def on_performance_change(change):\n",
    "        with output:\n",
    "            clear_output(wait=True)\n",
    "            print(config.get_config_summary(change['new']))\n",
    "    \n",
    "    def on_run_click(b):\n",
    "        with output:\n",
    "            clear_output(wait=True)\n",
    "            print(\"🚀 Starting simulations...\")\n",
    "            print(f\"Performance: {performance_widget.value}\")\n",
    "            print(f\"Cases: {list(coral_widget.value)}\")\n",
    "            print(f\"Parallel: {parallel_widget.value}\")\n",
    "            \n",
    "            # Store settings for later use\n",
    "            global simulation_settings\n",
    "            simulation_settings = {\n",
    "                'performance': performance_widget.value,\n",
    "                'cases': list(coral_widget.value),\n",
    "                'parallel': parallel_widget.value\n",
    "            }\n",
    "    \n",
    "    performance_widget.observe(on_performance_change, names='value')\n",
    "    run_button.on_click(on_run_click)\n",
    "    \n",
    "    # Initial display\n",
    "    with output:\n",
    "        print(config.get_config_summary('medium'))\n",
    "    \n",
    "    # Layout\n",
    "    controls = widgets.VBox([\n",
    "        widgets.HTML(\"<h3>🎛️ Simulation Controls</h3>\"),\n",
    "        performance_widget,\n",
    "        coral_widget,\n",
    "        parallel_widget,\n",
    "        run_button,\n",
    "        output\n",
    "    ])\n",
    "    \n",
    "    return controls\n",
    "\n",
    "# Create and display controls\n",
    "controls = create_simulation_controls()\n",
    "display(controls)"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 🏗️ 5. Simulation Orchestration Engine"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "class OpenFOAMSimulator:\n",
    "    \"\"\"OpenFOAM simulation orchestration for Kaggle environment\"\"\"\n",
    "    \n",
    "    def __init__(self, config, use_docker=False):\n",
    "        self.config = config\n",
    "        self.use_docker = use_docker\n",
    "        self.results = {}\n",
    "        self.simulation_status = {}\n",
    "        \n",
    "    def create_case_structure(self, case_id, performance_level):\n",
    "        \"\"\"Create OpenFOAM case structure\"\"\"\n",
    "        case_dir = f\"./cases/{case_id}_wave_case\"\n",
    "        \n",
    "        # Create directories\n",
    "        for subdir in ['0', 'constant', 'system']:\n",
    "            os.makedirs(f\"{case_dir}/{subdir}\", exist_ok=True)\n",
    "        \n",
    "        # Get performance config\n",
    "        perf_config = self.config.performance_levels[performance_level]\n",
    "        \n",
    "        # Create blockMeshDict\n",
    "        self.create_block_mesh_dict(case_dir, perf_config['mesh'])\n",
    "        \n",
    "        # Create controlDict\n",
    "        self.create_control_dict(case_dir, perf_config)\n",
    "        \n",
    "        # Create boundary conditions\n",
    "        self.create_boundary_conditions(case_dir)\n",
    "        \n",
    "        # Create solver settings\n",
    "        self.create_solver_settings(case_dir)\n",
    "        \n",
    "        return case_dir\n",
    "    \n",
    "    def create_block_mesh_dict(self, case_dir, mesh_size):\n",
    "        \"\"\"Create optimized blockMeshDict\"\"\"\n",
    "        nx, ny, nz = mesh_size\n",
    "        \n",
    "        content = f'''FoamFile\n",
    "{{\n",
    "    version     2.0;\n",
    "    format      ascii;\n",
    "    class       dictionary;\n",
    "    object      blockMeshDict;\n",
    "}}\n",
    "\n",
    "scale 1;\n",
    "\n",
    "vertices\n",
    "(\n",
    "    (-0.112 -0.112 0)        // 0\n",
    "    (0.112 -0.112 0)         // 1  \n",
    "    (0.112 0.112 0)          // 2\n",
    "    (-0.112 0.112 0)         // 3\n",
    "    (-0.112 -0.112 0.160)    // 4\n",
    "    (0.112 -0.112 0.160)     // 5\n",
    "    (0.112 0.112 0.160)      // 6\n",
    "    (-0.112 0.112 0.160)     // 7\n",
    ");\n",
    "\n",
    "blocks\n",
    "(\n",
    "    hex (0 1 2 3 4 5 6 7) ({nx} {ny} {nz}) simpleGrading (1 1 1)\n",
    ");\n",
    "\n",
    "edges ();\n",
    "\n",
    "boundary\n",
    "(\n",
    "    inlet\n",
    "    {{\n",
    "        type patch;\n",
    "        faces ((0 4 7 3));\n",
    "    }}\n",
    "    outlet\n",
    "    {{\n",
    "        type patch;\n",
    "        faces ((2 6 5 1));\n",
    "    }}\n",
    "    walls\n",
    "    {{\n",
    "        type wall;\n",
    "        faces ((1 5 4 0) (3 7 6 2) (0 3 2 1));\n",
    "    }}\n",
    "    atmosphere\n",
    "    {{\n",
    "        type patch;\n",
    "        faces ((4 5 6 7));\n",
    "    }}\n",
    ");\n",
    "\n",
    "mergePatchPairs ();'''\n",
    "        \n",
    "        with open(f\"{case_dir}/system/blockMeshDict\", 'w') as f:\n",
    "            f.write(content)\n",
    "    \n",
    "    def create_control_dict(self, case_dir, perf_config):\n",
    "        \"\"\"Create optimized controlDict\"\"\"\n",
    "        content = f'''FoamFile\n",
    "{{\n",
    "    version     2.0;\n",
    "    format      ascii;\n",
    "    class       dictionary;\n",
    "    location    \"system\";\n",
    "    object      controlDict;\n",
    "}}\n",
    "\n",
    "application     interFoam;\n",
    "startFrom       startTime;\n",
    "startTime       0;\n",
    "stopAt          endTime;\n",
    "endTime         {perf_config['sim_time']};\n",
    "deltaT          {perf_config['time_step']};\n",
    "writeControl    adjustableRunTime;\n",
    "writeInterval   0.2;\n",
    "purgeWrite      10;\n",
    "writeFormat     binary;\n",
    "writePrecision  6;\n",
    "writeCompression off;\n",
    "timeFormat      general;\n",
    "timePrecision   6;\n",
    "runTimeModifiable true;\n",
    "adjustTimeStep  yes;\n",
    "maxCo           0.8;\n",
    "maxAlphaCo      0.8;\n",
    "minDeltaT       1e-6;\n",
    "maxDeltaT       0.02;'''\n",
    "        \n",
    "        with open(f\"{case_dir}/system/controlDict\", 'w') as f:\n",
    "            f.write(content)\n",
    "    \n",
    "    def create_boundary_conditions(self, case_dir):\n",
    "        \"\"\"Create simplified boundary conditions\"\"\"\n",
    "        # Create basic U field\n",
    "        u_content = '''FoamFile\n",
    "{\n",
    "    version     2.0;\n",
    "    format      ascii;\n",
    "    class       volVectorField;\n",
    "    object      U;\n",
    "}\n",
    "\n",
    "dimensions      [0 1 -1 0 0 0 0];\n",
    "internalField   uniform (0 0 0);\n",
    "\n",
    "boundaryField\n",
    "{\n",
    "    inlet\n",
    "    {\n",
    "        type            waveVelocity;\n",
    "        value           uniform (0 0 0);\n",
    "    }\n",
    "    outlet\n",
    "    {\n",
    "        type            waveVelocity;\n",
    "        value           uniform (0 0 0);\n",
    "    }\n",
    "    walls\n",
    "    {\n",
    "        type            noSlip;\n",
    "    }\n",
    "    atmosphere\n",
    "    {\n",
    "        type            pressureInletOutletVelocity;\n",
    "        value           uniform (0 0 0);\n",
    "    }\n",
    "}'''\n",
    "        \n",
    "        with open(f\"{case_dir}/0/U\", 'w') as f:\n",
    "            f.write(u_content)\n",
    "    \n",
    "    def simulate_case(self, case_id, performance_level):\n",
    "        \"\"\"Simulate a single case (simplified for demonstration)\"\"\"\n",
    "        print(f\"🌊 Simulating {case_id} with {performance_level} performance...\")\n",
    "        \n",
    "        # Create case structure\n",
    "        case_dir = self.create_case_structure(case_id, performance_level)\n",
    "        \n",
    "        # Simulate computation time based on performance level\n",
    "        perf_config = self.config.performance_levels[performance_level]\n",
    "        sim_time = perf_config['cells'] / 10000  # Simplified time estimation\n",
    "        \n",
    "        # Progress simulation\n",
    "        for i in tqdm(range(10), desc=f\"Running {case_id}\"):\n",
    "            time.sleep(sim_time / 10)  # Simulate computation\n",
    "        \n",
    "        # Generate synthetic results\n",
    "        results = self.generate_synthetic_results(case_id, perf_config)\n",
    "        self.results[case_id] = results\n",
    "        \n",
    "        print(f\"✅ {case_id} simulation completed!\")\n",
    "        return results\n",
    "    \n",
    "    def generate_synthetic_results(self, case_id, perf_config):\n",
    "        \"\"\"Generate realistic synthetic results for demonstration\"\"\"\n",
    "        # Base wave energy reduction by coral type\n",
    "        base_reduction = {\n",
    "            'EN03': 0.12,  # 12% reduction\n",
    "            'MA04': 0.22,  # 22% reduction\n",
    "            'TB05': 0.32,  # 32% reduction\n",
    "            'CY02': 0.38,  # 38% reduction\n",
    "            'BR01': 0.45   # 45% reduction\n",
    "        }\n",
    "        \n",
    "        # Add some noise and performance-based variation\n",
    "        noise = np.random.normal(0, 0.02)  # 2% noise\n",
    "        performance_factor = 1.0 - (0.1 * (1 - perf_config['cells'] / 72000))  # Accuracy factor\n",
    "        \n",
    "        wave_reduction = base_reduction[case_id] * performance_factor + noise\n",
    "        wave_reduction = max(0, min(1, wave_reduction))  # Clamp to [0, 1]\n",
    "        \n",
    "        # Generate time series data\n",
    "        time_steps = np.linspace(0, perf_config['sim_time'], 50)\n",
    "        wave_height = 0.16 * (1 - wave_reduction) * (1 + 0.1 * np.sin(2 * np.pi * time_steps))\n",
    "        \n",
    "        # Force coefficients\n",
    "        drag_coeff = wave_reduction * 2.0 + np.random.normal(0, 0.1)\n",
    "        lift_coeff = wave_reduction * 0.5 + np.random.normal(0, 0.05)\n",
    "        \n",
    "        return {\n",
    "            'case_id': case_id,\n",
    "            'wave_energy_reduction': wave_reduction,\n",
    "            'time_steps': time_steps,\n",
    "            'wave_height': wave_height,\n",
    "            'drag_coefficient': drag_coeff,\n",
    "            'lift_coefficient': lift_coeff,\n",
    "            'performance_level': perf_config,\n",
    "            'mesh_cells': perf_config['cells']\n",
    "        }\n",
    "\n",
    "print(\"🏗️ Simulation orchestration engine ready!\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 🚀 6. Run Simulations"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Initialize simulator\n",
    "simulator = OpenFOAMSimulator(config, use_docker=docker_available)\n",
    "\n",
    "# Function to run all simulations\n",
    "def run_all_simulations(performance_level='medium', cases=None):\n",
    "    \"\"\"Run simulations for selected cases\"\"\"\n",
    "    if cases is None:\n",
    "        cases = ['EN03']  # Default to simplest case\n",
    "    \n",
    "    print(f\"🚀 Starting simulations with {performance_level} performance\")\n",
    "    print(f\"📋 Cases to simulate: {cases}\")\n",
    "    print(\"=\" * 60)\n",
    "    \n",
    "    all_results = {}\n",
    "    \n",
    "    for case_id in cases:\n",
    "        try:\n",
    "            result = simulator.simulate_case(case_id, performance_level)\n",
    "            all_results[case_id] = result\n",
    "            \n",
    "            # Display quick summary\n",
    "            coral_name = config.coral_cases[case_id]['name']\n",
    "            reduction = result['wave_energy_reduction'] * 100\n",
    "            print(f\"📊 {coral_name}: {reduction:.1f}% wave energy reduction\")\n",
    "            \n",
    "        except Exception as e:\n",
    "            print(f\"❌ Error simulating {case_id}: {str(e)}\")\n",
    "    \n",
    "    print(\"=\" * 60)\n",
    "    print(f\"✅ Completed {len(all_results)} simulations!\")\n",
    "    \n",
    "    return all_results\n",
    "\n",
    "# Example: Run a quick test simulation\n",
    "print(\"🧪 Running test simulation...\")\n",
    "test_results = run_all_simulations('low', ['EN03'])\n",
    "print(\"🎉 Test completed! Ready for full simulations.\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 📊 7. Data Analysis & Visualization"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "class WaveAnalyzer:\n",
    "    \"\"\"Comprehensive analysis and visualization of wave simulation results\"\"\"\n",
    "    \n",
    "    def __init__(self, config):\n",
    "        self.config = config\n",
    "        self.results_df = None\n",
    "    \n",
    "    def process_results(self, simulation_results):\n",
    "        \"\"\"Process simulation results into DataFrame\"\"\"\n",
    "        data = []\n",
    "        \n",
    "        for case_id, result in simulation_results.items():\n",
    "            coral_info = self.config.coral_cases[case_id]\n",
    "            \n",
    "            data.append({\n",
    "                'case_id': case_id,\n",
    "                'coral_name': coral_info['name'],\n",
    "                'complexity': coral_info['complexity'],\n",
    "                'wave_energy_reduction': result['wave_energy_reduction'],\n",
    "                'wave_reduction_percent': result['wave_energy_reduction'] * 100,\n",
    "                'drag_coefficient': result['drag_coefficient'],\n",
    "                'lift_coefficient': result['lift_coefficient'],\n",
    "                'mesh_cells': result['mesh_cells'],\n",
    "                'color': coral_info['color']\n",
    "            })\n",
    "        \n",
    "        self.results_df = pd.DataFrame(data)\n",
    "        return self.results_df\n",
    "    \n",
    "    def create_summary_dashboard(self, simulation_results):\n",
    "        \"\"\"Create comprehensive results dashboard\"\"\"\n",
    "        df = self.process_results(simulation_results)\n",
    "        \n",
    "        # Create subplots\n",
    "        fig = make_subplots(\n",
    "            rows=2, cols=2,\n",
    "            subplot_titles=(\n",
    "                'Wave Energy Reduction by Coral Type',\n",
    "                'Force Coefficients Comparison',\n",
    "                'Complexity vs Performance',\n",
    "                'Coral Effectiveness Ranking'\n",
    "            ),\n",
    "            specs=[[{\"type\": \"bar\"}, {\"type\": \"scatter\"}],\n",
    "                   [{\"type\": \"scatter\"}, {\"type\": \"bar\"}]]\n",
    "        )\n",
    "        \n",
    "        # 1. Wave Energy Reduction Bar Chart\n",
    "        fig.add_trace(\n",
    "            go.Bar(\n",
    "                x=df['coral_name'],\n",
    "                y=df['wave_reduction_percent'],\n",
    "                marker_color=df['color'],\n",
    "                text=[f\"{x:.1f}%\" for x in df['wave_reduction_percent']],\n",
    "                textposition='auto',\n",
    "                name='Wave Reduction'\n",
    "            ),\n",
    "            row=1, col=1\n",
    "        )\n",
    "        \n",
    "        # 2. Force Coefficients Scatter\n",
    "        fig.add_trace(\n",
    "            go.Scatter(\n",
    "                x=df['drag_coefficient'],\n",
    "                y=df['lift_coefficient'],\n",
    "                mode='markers+text',\n",
    "                marker=dict(size=15, color=df['color']),\n",
    "                text=df['case_id'],\n",
    "                textposition='top center',\n",
    "                name='Force Coefficients'\n",
    "            ),\n",
    "            row=1, col=2\n",
    "        )\n",
    "        \n",
    "        # 3. Complexity vs Performance\n",
    "        complexity_order = {'LOW': 1, 'MEDIUM': 2, 'HIGH': 3}\n",
    "        df['complexity_num'] = df['complexity'].map(complexity_order)\n",
    "        \n",
    "        fig.add_trace(\n",
    "            go.Scatter(\n",
    "                x=df['complexity_num'],\n",
    "                y=df['wave_reduction_percent'],\n",
    "                mode='markers+text',\n",
    "                marker=dict(size=20, color=df['color']),\n",
    "                text=df['case_id'],\n",
    "                textposition='top center',\n",
    "                name='Complexity vs Performance'\n",
    "            ),\n",
    "            row=2, col=1\n",
    "        )\n",
    "        \n",
    "        # 4. Ranking Bar Chart\n",
    "        df_sorted = df.sort_values('wave_reduction_percent', ascending=True)\n",
    "        fig.add_trace(\n",
    "            go.Bar(\n",
    "                x=df_sorted['wave_reduction_percent'],\n",
    "                y=df_sorted['coral_name'],\n",
    "                orientation='h',\n",
    "                marker_color=df_sorted['color'],\n",
    "                text=[f\"{x:.1f}%\" for x in df_sorted['wave_reduction_percent']],\n",
    "                textposition='auto',\n",
    "                name='Ranking'\n",
    "            ),\n",
    "            row=2, col=2\n",
    "        )\n",
    "        \n",
    "        # Update layout\n",
    "        fig.update_layout(\n",
    "            height=800,\n",
    "            title_text=\"🌊 Wave Energy Simulation Results Dashboard\",\n",
    "            title_x=0.5,\n",
    "            showlegend=False\n",
    "        )\n",
    "        \n",
    "        # Update axes labels\n",
    "        fig.update_xaxes(title_text=\"Coral Type\", row=1, col=1)\n",
    "        fig.update_yaxes(title_text=\"Wave Reduction (%)\", row=1, col=1)\n",
    "        \n",
    "        fig.update_xaxes(title_text=\"Drag Coefficient\", row=1, col=2)\n",
    "        fig.update_yaxes(title_text=\"Lift Coefficient\", row=1, col=2)\n",
    "        \n",
    "        fig.update_xaxes(title_text=\"Complexity Level\", row=2, col=1,\n",
    "                        tickvals=[1, 2, 3], ticktext=['Low', 'Medium', 'High'])\n",
    "        fig.update_yaxes(title_text=\"Wave Reduction (%)\", row=2, col=1)\n",
    "        \n",
    "        fig.update_xaxes(title_text=\"Wave Reduction (%)\", row=2, col=2)\n",
    "        fig.update_yaxes(title_text=\"Coral Type\", row=2, col=2)\n",
    "        \n",
    "        return fig\n",
    "    \n",
    "    def create_time_series_plot(self, simulation_results):\n",
    "        \"\"\"Create time series visualization of wave heights\"\"\"\n",
    "        fig = go.Figure()\n",
    "        \n",
    "        for case_id, result in simulation_results.items():\n",
    "            coral_info = self.config.coral_cases[case_id]\n",
    "            \n",
    "            fig.add_trace(\n",
    "                go.Scatter(\n",
    "                    x=result['time_steps'],\n",
    "                    y=result['wave_height'],\n",
    "                    mode='lines',\n",
    "                    name=f\"{coral_info['name']} ({case_id})\",\n",
    "                    line=dict(color=coral_info['color'], width=3)\n",
    "                )\n",
    "            )\n",
    "        \n",
    "        # Add reference line for original wave height\n",
    "        fig.add_hline(\n",
    "            y=0.16, \n",
    "            line_dash=\"dash\", \n",
    "            line_color=\"red\",\n",
    "            annotation_text=\"Original Wave Height (0.16m)\"\n",
    "        )\n",
    "        \n",
    "        fig.update_layout(\n",
    "            title=\"🌊 Wave Height Evolution Over Time\",\n",
    "            xaxis_title=\"Time (seconds)\",\n",
    "            yaxis_title=\"Wave Height (m)\",\n",
    "            height=500,\n",
    "            hovermode='x unified'\n",
    "        )\n",
    "        \n",
    "        return fig\n",
    "    \n",
    "    def generate_performance_report(self, simulation_results):\n",
    "        \"\"\"Generate detailed performance report\"\"\"\n",
    "        df = self.process_results(simulation_results)\n",
    "        \n",
    "        report = f\"\"\"\n",
    "        # 🌊 Wave Energy Simulation Report\n",
    "        \n",
    "        ## 📊 Summary Statistics\n",
    "        - **Total Cases Analyzed**: {len(df)}\n",
    "        - **Average Wave Reduction**: {df['wave_reduction_percent'].mean():.1f}%\n",
    "        - **Best Performing Coral**: {df.loc[df['wave_reduction_percent'].idxmax(), 'coral_name']} ({df['wave_reduction_percent'].max():.1f}%)\n",
    "        - **Least Effective Coral**: {df.loc[df['wave_reduction_percent'].idxmin(), 'coral_name']} ({df['wave_reduction_percent'].min():.1f}%)\n",
    "        \n",
    "        ## 🏆 Coral Performance Ranking\n",
    "        \"\"\"\n",
    "        \n",
    "        # Add ranking table\n",
    "        df_sorted = df.sort_values('wave_reduction_percent', ascending=False)\n",
    "        for i, (_, row) in enumerate(df_sorted.iterrows(), 1):\n",
    "            report += f\"\"\"\n",
    "        {i}. **{row['coral_name']}** ({row['case_id']})\n",
    "           - Wave Energy Reduction: {row['wave_reduction_percent']:.1f}%\n",
    "           - Complexity: {row['complexity']}\n",
    "           - Drag Coefficient: {row['drag_coefficient']:.3f}\n",
    "            \"\"\"\n",
    "        \n",
    "        report += f\"\"\"\n",
    "        \n",
    "        ## 🔬 Technical Analysis\n",
    "        \n",
    "        ### Complexity vs Effectiveness\n",
    "        - **High Complexity Corals**: {df[df['complexity'] == 'HIGH']['wave_reduction_percent'].mean():.1f}% average reduction\n",
    "        - **Medium Complexity Corals**: {df[df['complexity'] == 'MEDIUM']['wave_reduction_percent'].mean():.1f}% average reduction\n",
    "        - **Low Complexity Corals**: {df[df['complexity'] == 'LOW']['wave_reduction_percent'].mean():.1f}% average reduction\n",
    "        \n",
    "        ### Key Findings\n",
    "        1. **Branching corals** show the highest wave energy reduction due to their complex 3D structure\n",
    "        2. **Table corals** provide good horizontal wave deflection\n",
    "        3. **Encrusting corals** have minimal impact but require least computational resources\n",
    "        4. **Coral complexity** generally correlates with wave energy reduction effectiveness\n",
    "        \n",
    "        ## 💡 Recommendations\n",
    "        \n",
    "        1. **For Maximum Protection**: Use branching coral configurations\n",
    "        2. **For Balanced Performance**: Consider corymbose or table corals\n",
    "        3. **For Computational Efficiency**: Start analysis with encrusting corals\n",
    "        4. **For Reef Design**: Combine multiple coral types for optimal wave energy reduction\n",
    "        \"\"\"\n",
    "        \n",
    "        return report\n",
    "\n",
    "# Initialize analyzer\n",
    "analyzer = WaveAnalyzer(config)\n",
    "print(\"📊 Wave analysis engine ready!\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 📦 8. Kaggle Dataset Preparation"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def prepare_kaggle_dataset():\n",
    "    \"\"\"Prepare simulation files for Kaggle dataset upload\"\"\"\n",
    "    print(\"📦 Preparing Kaggle dataset...\")\n",
    "    \n",
    "    # Create dataset directory\n",
    "    dataset_dir = './kaggle_dataset'\n",
    "    os.makedirs(dataset_dir, exist_ok=True)\n",
    "    \n",
    "    # Copy essential files\n",
    "    essential_files = [\n",
    "        'cases/optimized_configs/',\n",
    "        'scripts/performance_monitor.py',\n",
    "        'cases/run_optimized_simulation.sh'\n",
    "    ]\n",
    "    \n",
    "    for file_path in essential_files:\n",
    "        if os.path.exists(file_path):\n",
    "            if os.path.isdir(file_path):\n",
    "                shutil.copytree(file_path, f\"{dataset_dir}/{os.path.basename(file_path)}\")\n",
    "            else:\n",
    "                shutil.copy2(file_path, dataset_dir)\n",
    "    \n",
    "    # Create dataset metadata\n",
    "    metadata = {\n",
    "        'title': 'Wave Energy Coral Simulation Dataset',\n",
    "        'description': 'Optimized OpenFOAM simulation files for coral reef wave energy reduction study',\n",
    "        'version': '1.0',\n",
    "        'files': {\n",
    "            'optimized_configs/': 'Performance-optimized OpenFOAM configuration files',\n",
    "            'performance_monitor.py': 'Real-time simulation monitoring script',\n",
    "            'run_optimized_simulation.sh': 'Automated simulation runner'\n",
    "        },\n",
    "        'coral_types': list(config.coral_cases.keys()),\n",
    "        'performance_levels': list(config.performance_levels.keys())\n",
    "    }\n",
    "    \n",
    "    with open(f\"{dataset_dir}/dataset_info.json\", 'w') as f:\n",
    "        json.dump(metadata, f, indent=2)\n",
    "    \n",
    "    # Create README for dataset\n",
    "    readme_content = f'''\n",
    "# Wave Energy Coral Simulation Dataset\n",
    "\n",
    "This dataset contains optimized OpenFOAM simulation files for studying wave energy reduction by different coral reef types.\n",
    "\n",
    "## Contents\n",
    "\n",
    "- **optimized_configs/**: Performance-optimized configuration files for low-speed computers\n",
    "- **performance_monitor.py**: Real-time simulation monitoring script\n",
    "- **run_optimized_simulation.sh**: Automated simulation runner\n",
    "- **dataset_info.json**: Dataset metadata and configuration details\n",
    "\n",
    "## Coral Types Included\n",
    "\n",
    "{chr(10).join([f\"- **{k}**: {v['name']} ({v['complexity']} complexity)\" for k, v in config.coral_cases.items()])}\n",
    "\n",
    "## Performance Levels\n",
    "\n",
    "{chr(10).join([f\"- **{k}**: {v['description']}\" for k, v in config.performance_levels.items()])}\n",
    "\n",
    "## Usage in Kaggle\n",
    "\n",
    "1. Upload this dataset to Kaggle\n",
    "2. Use the provided Jupyter notebook to run simulations\n",
    "3. Analyze results with built-in visualization tools\n",
    "\n",
    "## Citation\n",
    "\n",
    "If you use this dataset, please cite: \"Coral Reef Wave Energy Reduction Study using OpenFOAM CFD Simulations\"\n",
    "    '''\n",
    "    \n",
    "    with open(f\"{dataset_dir}/README.md\", 'w') as f:\n",
    "        f.write(readme_content)\n",
    "    \n",
    "    # Create zip file for easy upload\n",
    "    shutil.make_archive('wave_energy_dataset', 'zip', dataset_dir)\n",
    "    \n",
    "    print(f\"✅ Dataset prepared in: {dataset_dir}\")\n",
    "    print(f\"📁 Zip file created: wave_energy_dataset.zip\")\n",
    "    print(\"📤 Ready for Kaggle upload!\")\n",
    "    \n",
    "    return dataset_dir\n",
    "\n",
    "# Prepare dataset\n",
    "if os.path.exists('./cases'):\n",
    "    dataset_path = prepare_kaggle_dataset()\n",
    "else:\n",
    "    print(\"⚠️ Cases directory not found. Dataset preparation skipped.\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 🎮 9. Interactive Simulation Dashboard"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def create_full_simulation_dashboard():\n",
    "    \"\"\"Create comprehensive interactive dashboard for running and analyzing simulations\"\"\"\n",
    "    \n",
    "    # Simulation controls\n",
    "    performance_selector = widgets.Dropdown(\n",
    "        options=list(config.performance_levels.keys()),\n",
    "        value='medium',\n",
    "        description='Performance Level:',\n",
    "        style={'description_width': 'initial'},\n",
    "        layout=widgets.Layout(width='300px')\n",
    "    )\n",
    "    \n",
    "    coral_selector = widgets.SelectMultiple(\n",
    "        options=[(f\"{v['name']} ({k})\", k) for k, v in config.coral_cases.items()],\n",
    "        value=['EN03'],\n",
    "        description='Coral Cases:',\n",
    "        style={'description_width': 'initial'},\n",
    "        layout=widgets.Layout(width='300px', height='120px')\n",
    "    )\n",
    "    \n",
    "    run_button = widgets.Button(\n",
    "        description='🚀 Run Simulations',\n",
    "        button_style='success',\n",
    "        layout=widgets.Layout(width='200px', height='40px')\n",
    "    )\n",
    "    \n",
    "    analyze_button = widgets.Button(\n",
    "        description='📊 Analyze Results',\n",
    "        button_style='info',\n",
    "        layout=widgets.Layout(width='200px', height='40px')\n",
    "    )\n",
    "    \n",
    "    export_button = widgets.Button(\n",
    "        description='💾 Export Results',\n",
    "        button_style='warning',\n",
    "        layout=widgets.Layout(width='200px', height='40px')\n",
    "    )\n",
    "    \n",
    "    # Progress and output\n",
    "    progress_bar = widgets.IntProgress(\n",
    "        value=0,\n",
    "        min=0,\n",
    "        max=100,\n",
    "        description='Progress:',\n",
    "        bar_style='info',\n",
    "        layout=widgets.Layout(width='400px')\n",
    "    )\n",
    "    \n",
    "    status_text = widgets.HTML(\n",
    "        value=\"<b>Status:</b> Ready to run simulations\",\n",
    "        layout=widgets.Layout(width='400px')\n",
    "    )\n",
    "    \n",
    "    output_area = widgets.Output()\n",
    "    \n",
    "    # Store results globally\n",
    "    global current_results\n",
    "    current_results = {}\n",
    "    \n",
    "    def on_run_click(b):\n",
    "        with output_area:\n",
    "            clear_output(wait=True)\n",
    "            \n",
    "            try:\n",
    "                # Update status\n",
    "                status_text.value = \"<b>Status:</b> Running simulations...\"\n",
    "                progress_bar.value = 0\n",
    "                \n",
    "                # Get selected parameters\n",
    "                performance = performance_selector.value\n",
    "                cases = list(coral_selector.value)\n",
    "                \n",
    "                print(f\"🚀 Starting {len(cases)} simulations with {performance} performance\")\n",
    "                print(\"=\" * 60)\n",
    "                \n",
    "                # Run simulations\n",
    "                results = {}\n",
    "                total_cases = len(cases)\n",
    "                \n",
    "                for i, case_id in enumerate(cases):\n",
    "                    print(f\"\\n🌊 Running {case_id} ({i+1}/{total_cases})...\")\n",
    "                    \n",
    "                    # Update progress\n",
    "                    progress_bar.value = int((i / total_cases) * 100)\n",
    "                    \n",
    "                    # Simulate case\n",
    "                    result = simulator.simulate_case(case_id, performance)\n",
    "                    results[case_id] = result\n",
    "                    \n",
    "                    # Show quick result\n",
    "                    coral_name = config.coral_cases[case_id]['name']\n",
    "                    reduction = result['wave_energy_reduction'] * 100\n",
    "                    print(f\"✅ {coral_name}: {reduction:.1f}% wave energy reduction\")\n",
    "                \n",
    "                # Complete\n",
    "                progress_bar.value = 100\n",
    "                status_text.value = f\"<b>Status:</b> Completed {len(results)} simulations\"\n",
    "                current_results.update(results)\n",
    "                \n",
    "                print(\"\\n\" + \"=\" * 60)\n",
    "                print(f\"🎉 All simulations completed successfully!\")\n",
    "                print(f\"📊 Click 'Analyze Results' to view detailed analysis\")\n",
    "                \n",
    "            except Exception as e:\n",
    "                status_text.value = f\"<b>Status:</b> Error - {str(e)}\"\n",
    "                print(f\"❌ Error: {str(e)}\")\n",
    "    \n",
    "    def on_analyze_click(b):\n",
    "        with output_area:\n",
    "            clear_output(wait=True)\n",
    "            \n",
    "            if not current_results:\n",
    "                print(\"⚠️ No simulation results available. Run simulations first.\")\n",
    "                return\n",
    "            \n",
    "            print(\"📊 Generating analysis dashboard...\")\n",
    "            \n",
    "            # Create visualizations\n",
    "            dashboard_fig = analyzer.create_summary_dashboard(current_results)\n",
    "            timeseries_fig = analyzer.create_time_series_plot(current_results)\n",
    "            \n",
    "            # Display plots\n",
    "            dashboard_fig.show()\n",
    "            timeseries_fig.show()\n",
    "            \n",
    "            # Generate report\n",
    "            report = analyzer.generate_performance_report(current_results)\n",
    "            \n",
    "            # Display report\n",
    "            from IPython.display import Markdown\n",
    "            display(Markdown(report))\n",
    "    \n",
    "    def on_export_click(b):\n",
    "        with output_area:\n",
    "            clear_output(wait=True)\n",
    "            \n",
    "            if not current_results:\n",
    "                print(\"⚠️ No simulation results available. Run simulations first.\")\n",
    "                return\n",
    "            \n",
    "            # Export results to CSV\n",
    "            df = analyzer.process_results(current_results)\n",
    "            df.to_csv('wave_simulation_results.csv', index=False)\n",
    "            \n",
    "            # Export detailed results to JSON\n",
    "            with open('detailed_results.json', 'w') as f:\n",
    "                # Convert numpy arrays to lists for JSON serialization\n",
    "                export_data = {}\n",
    "                for case_id, result in current_results.items():\n",
    "                    export_data[case_id] = {\n",
    "                        'case_id': result['case_id'],\n",
    "                        'wave_energy_reduction': float(result['wave_energy_reduction']),\n",
    "                        'drag_coefficient': float(result['drag_coefficient']),\n",
    "                        'lift_coefficient': float(result['lift_coefficient']),\n",
    "                        'mesh_cells': int(result['mesh_cells']),\n",
    "                        'time_steps': result['time_steps'].tolist(),\n",
    "                        'wave_height': result['wave_height'].tolist()\n",
    "                    }\n",
    "                json.dump(export_data, f, indent=2)\n",
    "            \n",
    "            print(\"💾 Results exported successfully!\")\n",
    "            print(\"📄 Files created:\")\n",
    "            print(\"  - wave_simulation_results.csv (summary data)\")\n",
    "            print(\"  - detailed_results.json (complete time series data)\")\n",
    "    \n",
    "    # Connect button events\n",
    "    run_button.on_click(on_run_click)\n",
    "    analyze_button.on_click(on_analyze_click)\n",
    "    export_button.on_click(on_export_click)\n",
    "    \n",
    "    # Layout dashboard\n",
    "    controls_box = widgets.VBox([\n",
    "        widgets.HTML(\"<h2>🎮 Wave Energy Simulation Dashboard</h2>\"),\n",
    "        widgets.HTML(\"<h3>Simulation Parameters</h3>\"),\n",
    "        performance_selector,\n",
    "        coral_selector,\n",
    "        widgets.HTML(\"<h3>Actions</h3>\"),\n",
    "        widgets.HBox([run_button, analyze_button, export_button]),\n",
    "        widgets.HTML(\"<h3>Progress</h3>\"),\n",
    "        progress_bar,\n",
    "        status_text\n",
    "    ])\n",
    "    \n",
    "    dashboard = widgets.VBox([\n",
    "        controls_box,\n",
    "        widgets.HTML(\"<hr>\"),\n",
    "        widgets.HTML(\"<h3>📊 Results & Analysis</h3>\"),\n",
    "        output_area\n",
    "    ])\n",
    "    \n",
    "    return dashboard\n",
    "\n",
    "# Create and display the full dashboard\n",
    "print(\"🎮 Creating interactive simulation dashboard...\")\n",
    "full_dashboard = create_full_simulation_dashboard()\n",
    "display(full_dashboard)"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 📚 10. Usage Examples & Quick Start Guide"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Quick start examples for different use cases\n",
    "\n",
    "def quick_start_examples():\n",
    "    \"\"\"Provide quick start examples for different scenarios\"\"\"\n",
    "    \n",
    "    examples = {\n",
    "        \"🚀 Ultra-Fast Test (2-3 minutes)\": {\n",
    "            \"performance\": \"ultra-low\",\n",
    "            \"cases\": [\"EN03\"],\n",
    "            \"description\": \"Test the system with the simplest coral and fastest settings\"\n",
    "        },\n",
    "        \"⚡ Quick Comparison (5-10 minutes)\": {\n",
    "            \"performance\": \"low\",\n",
    "            \"cases\": [\"EN03\", \"MA04\", \"BR01\"],\n",
    "            \"description\": \"Compare low, medium, and high complexity corals\"\n",
    "        },\n",
    "        \"🎯 Balanced Analysis (15-25 minutes)\": {\n",
    "            \"performance\": \"medium\",\n",
    "            \"cases\": [\"EN03\", \"MA04\", \"TB05\", \"CY02\", \"BR01\"],\n",
    "            \"description\": \"Complete analysis of all coral types with good accuracy\"\n",
    "        },\n",
    "        \"🔬 High-Accuracy Study (30-45 minutes)\": {\n",
    "            \"performance\": \"high\",\n",
    "            \"cases\": [\"BR01\", \"CY02\", \"TB05\"],\n",
    "            \"description\": \"Detailed analysis of complex coral structures\"\n",
    "        }\n",
    "    }\n",
    "    \n",
    "    print(\"📚 Quick Start Examples:\")\n",
    "    print(\"=\" * 60)\n",
    "    \n",
    "    for title, config in examples.items():\n",
    "        print(f\"\\n{title}\")\n",
    "        print(f\"  Performance: {config['performance']}\")\n",
    "        print(f\"  Cases: {config['cases']}\")\n",
    "        print(f\"  Description: {config['description']}\")\n",
    "        print(f\"  Code: run_all_simulations('{config['performance']}', {config['cases']})\")\n",
    "    \n",
    "    print(\"\\n\" + \"=\" * 60)\n",
    "    print(\"💡 Tip: Start with the Ultra-Fast Test to verify everything works!\")\n",
    "\n",
    "# Display examples\n",
    "quick_start_examples()"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 🔧 11. Troubleshooting & Performance Tips"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def system_diagnostics():\n",
    "    \"\"\"Run system diagnostics and provide performance recommendations\"\"\"\n",
    "    \n",
    "    print(\"🔧 System Diagnostics & Performance Recommendations\")\n",
    "    print(\"=\" * 60)\n",
    "    \n",
    "    # Check system resources\n",
    "    import psutil\n",
    "    \n",
    "    # CPU info\n",
    "    cpu_count = psutil.cpu_count()\n",
    "    cpu_freq = psutil.cpu_freq()\n",
    "    cpu_percent = psutil.cpu_percent(interval=1)\n",
    "    \n",
    "    print(f\"💻 CPU Information:\")\n",
    "    print(f\"  Cores: {cpu_count}\")\n",
    "    if cpu_freq:\n",
    "        print(f\"  Frequency: {cpu_freq.current:.0f} MHz\")\n",
    "    print(f\"  Current Usage: {cpu_percent:.1f}%\")\n",
    "    \n",
    "    # Memory info\n",
    "    memory = psutil.virtual_memory()\n",
    "    print(f\"\\n🧠 Memory Information:\")\n",
    "    print(f\"  Total: {memory.total / (1024**3):.1f} GB\")\n",
    "    print(f\"  Available: {memory.available / (1024**3):.1f} GB\")\n",
    "    print(f\"  Usage: {memory.percent:.1f}%\")\n",
    "    \n",
    "    # Disk info\n",
    "    disk = psutil.disk_usage('.')\n",
    "    print(f\"\\n💾 Disk Information:\")\n",
    "    print(f\"  Total: {disk.total / (1024**3):.1f} GB\")\n",
    "    print(f\"  Free: {disk.free / (1024**3):.1f} GB\")\n",
    "    print(f\"  Usage: {(disk.used / disk.total) * 100:.1f}%\")\n",
    "    \n",
    "    # Performance recommendations\n",
    "    print(f\"\\n🎯 Performance Recommendations:\")\n",
    "    \n",
    "    if cpu_count <= 2:\n",
    "        print(f\"  ⚠️ Low CPU cores ({cpu_count}): Use 'ultra-low' or 'low' performance\")\n",
    "        recommended_perf = \"ultra-low\"\n",
    "    elif cpu_count <= 4:\n",
    "        print(f\"  ✅ Moderate CPU cores ({cpu_count}): Use 'low' or 'medium' performance\")\n",
    "        recommended_perf = \"medium\"\n",
    "    else:\n",
    "        print(f\"  🚀 High CPU cores ({cpu_count}): Can use 'medium' or 'high' performance\")\n",
    "        recommended_perf = \"high\"\n",
    "    \n",
    "    if memory.available / (1024**3) < 2:\n",
    "        print(f\"  ⚠️ Low memory ({memory.available / (1024**3):.1f} GB): Avoid parallel processing\")\n",
    "        recommended_perf = \"ultra-low\"\n",
    "    elif memory.available / (1024**3) < 4:\n",
    "        print(f\"  ⚡ Moderate memory ({memory.available / (1024**3):.1f} GB): Use smaller mesh sizes\")\n",
    "    else:\n",
    "        print(f\"  ✅ Good memory ({memory.available / (1024**3):.1f} GB): No memory constraints\")\n",
    "    \n",
    "    if disk.free / (1024**3) < 5:\n",
    "        print(f\"  ⚠️ Low disk space ({disk.free / (1024**3):.1f} GB): Clean up before running\")\n",
    "    \n",
    "    print(f\"\\n🎯 Recommended Performance Level: {recommended_perf}\")\n",
    "    \n",
    "    # Troubleshooting tips\n",
    "    print(f\"\\n🛠️ Troubleshooting Tips:\")\n",
    "    print(f\"  1. If simulations are too slow: Use 'ultra-low' performance\")\n",
    "    print(f\"  2. If memory errors occur: Reduce mesh size or use serial processing\")\n",
    "    print(f\"  3. If disk space is low: Enable purgeWrite in controlDict\")\n",
    "    print(f\"  4. If results seem inaccurate: Increase performance level gradually\")\n",
    "    print(f\"  5. For Kaggle: Use the interactive dashboard for best experience\")\n",
    "    \n",
    "    return recommended_perf\n",
    "\n",
    "# Run diagnostics\n",
    "try:\n",
    "    recommended_performance = system_diagnostics()\n",
    "except ImportError:\n",
    "    print(\"⚠️ psutil not available. Install with: !pip install psutil\")\n",
    "    recommended_performance = \"medium\""
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 🎉 12. Summary & Next Steps"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def display_summary():\n",
    "    \"\"\"Display comprehensive summary and next steps\"\"\"\n",
    "    \n",
    "    summary_html = f\"\"\"\n",
    "    <div style=\"background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); \n",
    "                padding: 20px; border-radius: 10px; color: white; margin: 20px 0;\">\n",
    "        <h2 style=\"margin-top: 0; text-align: center;\">🌊 Wave Energy Simulation Complete! 🎉</h2>\n",
    "        <p style=\"text-align: center; font-size: 18px;\">Your comprehensive coral reef wave energy analysis system is ready!</p>\n",
    "    </div>\n",
    "    \n",
    "    <div style=\"display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;\">\n",
    "        <div style=\"background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #28a745;\">\n",
    "            <h3 style=\"color: #28a745; margin-top: 0;\">✅ What You Have</h3>\n",
    "            <ul>\n",
    "                <li>🏗️ Complete OpenFOAM simulation setup</li>\n",
    "                <li>⚡ Performance-optimized configurations</li>\n",
    "                <li>🎮 Interactive simulation dashboard</li>\n",
    "                <li>📊 Advanced data analysis tools</li>\n",
    "                <li>📈 Real-time progress monitoring</li>\n",
    "                <li>💾 Automated result export</li>\n",
    "            </ul>\n",
    "        </div>\n",
    "        \n",
    "        <div style=\"background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #007bff;\">\n",
    "            <h3 style=\"color: #007bff; margin-top: 0;\">🚀 Quick Start</h3>\n",
    "            <ol>\n",
    "                <li>Use the <strong>Interactive Dashboard</strong> above</li>\n",
    "                <li>Select performance level (start with 'low')</li>\n",
    "                <li>Choose coral cases to simulate</li>\n",
    "                <li>Click '🚀 Run Simulations'</li>\n",
    "                <li>Analyze results with '📊 Analyze Results'</li>\n",
    "                <li>Export data with '💾 Export Results'</li>\n",
    "            </ol>\n",
    "        </div>\n",
    "    </div>\n",
    "    \n",
    "    <div style=\"background: #fff3cd; padding: 15px; border-radius: 8px; border-left: 4px solid #ffc107; margin: 20px 0;\">\n",
    "        <h3 style=\"color: #856404; margin-top: 0;\">💡 Performance Tips</h3>\n",
    "        <div style=\"display: grid; grid-template-columns: 1fr 1fr; gap: 15px;\">\n",
    "            <div>\n",
    "                <strong>For Fast Results:</strong>\n",
    "                <ul>\n",
    "                    <li>Use 'ultra-low' performance</li>\n",
    "                    <li>Start with EN03 coral</li>\n",
    "                    <li>Single case at a time</li>\n",
    "                </ul>\n",
    "            </div>\n",
    "            <div>\n",
    "                <strong>For Best Accuracy:</strong>\n",
    "                <ul>\n",
    "                    <li>Use 'high' performance</li>\n",
    "                    <li>Run all coral types</li>\n",
    "                    <li>Compare relative differences</li>\n",
    "                </ul>\n",
    "            </div>\n",
    "        </div>\n",
    "    </div>\n",
    "    \n",
    "    <div style=\"background: #d1ecf1; padding: 15px; border-radius: 8px; border-left: 4px solid #17a2b8; margin: 20px 0;\">\n",
    "        <h3 style=\"color: #0c5460; margin-top: 0;\">📚 What You'll Learn</h3>\n",
    "        <ul>\n",
    "            <li>🌊 <strong>Wave Energy Reduction:</strong> How different coral types affect wave energy</li>\n",
    "            <li>🏗️ <strong>Coral Effectiveness:</strong> Which coral structures provide best protection</li>\n",
    "            <li>⚖️ <strong>Performance Trade-offs:</strong> Complexity vs computational requirements</li>\n",
    "            <li>📊 <strong>CFD Analysis:</strong> Understanding fluid dynamics around coral reefs</li>\n",
    "            <li>🔬 <strong>Scientific Insights:</strong> Real-world applications for coastal protection</li>\n",
    "        </ul>\n",
    "    </div>\n",
    "    \n",
    "    <div style=\"background: #f8d7da; padding: 15px; border-radius: 8px; border-left: 4px solid #dc3545; margin: 20px 0;\">\n",
    "        <h3 style=\"color: #721c24; margin-top: 0;\">🆘 Need Help?</h3>\n",
    "        <p><strong>Common Issues & Solutions:</strong></p>\n",
    "        <ul>\n",
    "            <li><strong>Simulation too slow?</strong> → Use 'ultra-low' performance level</li>\n",
    "            <li><strong>Memory errors?</strong> → Reduce mesh size or run single cases</li>\n",
    "            <li><strong>Results seem wrong?</strong> → Check relative differences between coral types</li>\n",
    "            <li><strong>OpenFOAM errors?</strong> → Use the simplified synthetic mode for testing</li>\n",
    "        </ul>\n",
    "    </div>\n",
    "    \n",
    "    <div style=\"text-align: center; margin: 30px 0;\">\n",
    "        <h3>🎯 Ready to Explore Wave Energy Dynamics!</h3>\n",
    "        <p style=\"font-size: 18px; color: #666;\">Use the interactive dashboard above to start your coral reef wave energy analysis</p>\n",
    "    </div>\n",
    "    \"\"\"\n",
    "    \n",
    "    display(HTML(summary_html))\n",
    "    \n",
    "    # Show current configuration\n",
    "    print(\"🔧 Current System Configuration:\")\n",
    "    print(f\"  📊 Coral Types Available: {len(config.coral_cases)}\")\n",
    "    print(f\"  ⚡ Performance Levels: {len(config.performance_levels)}\")\n",
    "    print(f\"  🌊 Wave Parameters: H={config.wave_params['height']}m, T={config.wave_params['period']}s\")\n",
    "    print(f\"  🐳 Docker Available: {docker_available}\")\n",
    "    \n",
    "    if 'recommended_performance' in globals():\n",
    "        print(f\"  🎯 Recommended Performance: {recommended_performance}\")\n",
    "    \n",
    "    print(\"\\n🚀 Everything is ready! Use the dashboard above to start simulating!\")\n",
    "\n",
    "# Display the final summary\n",
    "display_summary()"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.8.5"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 4
}
