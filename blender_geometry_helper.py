#!/usr/bin/env python3
"""
Blender Geometry Helper for Coral Reef Wave Energy Simulations
Automates geometry preparation tasks in Blender with SnappyHexMesh GUI

Usage in Blender:
1. Open Blender with your coral geometry
2. Run this script in Blender's Text Editor
3. Use the helper functions to prepare geometry

Requirements:
- Blender with SnappyHexMesh GUI add-on installed
- Coral geometry imported and positioned
"""

import bpy
import bmesh
import mathutils
from mathutils import Vector
import os

class CoralGeometryHelper:
    """Helper class for coral geometry preparation in Blender"""
    
    def __init__(self):
        self.coral_height_target = 0.05  # 5cm target height
        self.domain_bounds = {
            'x_min': -0.2, 'x_max': 1.1,    # 1.3m length
            'y_min': -0.1, 'y_max': 0.1,    # 0.2m width  
            'z_min': 0.002, 'z_max': 0.3    # 0.3m height
        }
    
    def clear_scene(self, keep_coral=True):
        """Clear scene except coral geometry"""
        coral_objects = []
        if keep_coral:
            # Identify coral objects (assume they contain 'coral' in name)
            coral_objects = [obj for obj in bpy.context.scene.objects 
                           if 'coral' in obj.name.lower()]
        
        # Delete all other objects
        bpy.ops.object.select_all(action='SELECT')
        for obj in coral_objects:
            obj.select_set(False)
        bpy.ops.object.delete()
        
        # Deselect all
        bpy.ops.object.select_all(action='DESELECT')
        
        print(f"Scene cleared, kept {len(coral_objects)} coral objects")
    
    def analyze_coral_geometry(self):
        """Analyze coral geometry and provide scaling recommendations"""
        coral_objects = [obj for obj in bpy.context.scene.objects 
                        if obj.type == 'MESH' and 'coral' in obj.name.lower()]
        
        if not coral_objects:
            print("❌ No coral objects found. Import coral geometry first.")
            return None
        
        # Get combined bounds of all coral objects
        all_coords = []
        for obj in coral_objects:
            # Get world coordinates of all vertices
            mesh = obj.data
            for vertex in mesh.vertices:
                world_coord = obj.matrix_world @ vertex.co
                all_coords.append(world_coord)
        
        if not all_coords:
            print("❌ No vertices found in coral objects")
            return None
        
        # Calculate bounds
        min_coords = Vector((min(v.x for v in all_coords),
                           min(v.y for v in all_coords),
                           min(v.z for v in all_coords)))
        max_coords = Vector((max(v.x for v in all_coords),
                           max(v.y for v in all_coords),
                           max(v.z for v in all_coords)))
        
        dimensions = max_coords - min_coords
        center = (min_coords + max_coords) / 2
        
        analysis = {
            'objects': coral_objects,
            'min_coords': min_coords,
            'max_coords': max_coords,
            'dimensions': dimensions,
            'center': center,
            'current_height': dimensions.z
        }
        
        print(f"🪸 Coral Analysis:")
        print(f"  Objects found: {len(coral_objects)}")
        print(f"  Current height: {dimensions.z:.3f}m")
        print(f"  Target height: {self.coral_height_target:.3f}m")
        print(f"  Scale factor needed: {self.coral_height_target / dimensions.z:.3f}")
        print(f"  Center: ({center.x:.3f}, {center.y:.3f}, {center.z:.3f})")
        
        return analysis
    
    def scale_coral_to_target(self):
        """Scale coral to target height (5cm)"""
        analysis = self.analyze_coral_geometry()
        if not analysis:
            return False
        
        scale_factor = self.coral_height_target / analysis['current_height']
        
        # Select all coral objects
        bpy.ops.object.select_all(action='DESELECT')
        for obj in analysis['objects']:
            obj.select_set(True)
        
        # Set active object
        bpy.context.view_layer.objects.active = analysis['objects'][0]
        
        # Scale uniformly
        bpy.ops.transform.resize(value=(scale_factor, scale_factor, scale_factor))
        
        print(f"✅ Coral scaled by factor {scale_factor:.3f}")
        return True
    
    def position_coral_at_origin(self):
        """Position coral at domain origin"""
        analysis = self.analyze_coral_geometry()
        if not analysis:
            return False
        
        # Calculate offset to move center to origin
        offset = -analysis['center']
        offset.z = -analysis['min_coords'].z + 0.005  # Place on seabed
        
        # Select all coral objects
        bpy.ops.object.select_all(action='DESELECT')
        for obj in analysis['objects']:
            obj.select_set(True)
        
        # Move to origin
        bpy.ops.transform.translate(value=offset)
        
        print(f"✅ Coral positioned at origin")
        return True
    
    def create_domain_boundaries(self):
        """Create domain boundary surfaces"""
        
        # Create Ground (seabed)
        bpy.ops.mesh.primitive_plane_add(
            size=1, 
            location=(0.45, 0, 0.002)  # Center of domain
        )
        ground = bpy.context.active_object
        ground.name = "Ground"
        ground.scale = (0.65, 0.1, 1)  # 1.3m x 0.2m
        
        # Create Inlet
        bpy.ops.mesh.primitive_plane_add(
            size=1,
            location=(-0.2, 0, 0.151)  # Left boundary
        )
        inlet = bpy.context.active_object
        inlet.name = "Inlet"
        inlet.scale = (1, 0.1, 0.15)  # Height x Width
        inlet.rotation_euler = (0, 1.5708, 0)  # Rotate 90° around Y
        
        # Create Outlet  
        bpy.ops.mesh.primitive_plane_add(
            size=1,
            location=(1.1, 0, 0.151)  # Right boundary
        )
        outlet = bpy.context.active_object
        outlet.name = "Outlet"
        outlet.scale = (1, 0.1, 0.15)
        outlet.rotation_euler = (0, 1.5708, 0)
        
        # Create Walls (combined front, back, bottom)
        # Front wall
        bpy.ops.mesh.primitive_plane_add(
            size=1,
            location=(0.45, -0.1, 0.151)
        )
        wall_front = bpy.context.active_object
        wall_front.name = "Wall_Front"
        wall_front.scale = (0.65, 1, 0.15)
        wall_front.rotation_euler = (1.5708, 0, 0)
        
        # Back wall
        bpy.ops.mesh.primitive_plane_add(
            size=1,
            location=(0.45, 0.1, 0.151)
        )
        wall_back = bpy.context.active_object
        wall_back.name = "Wall_Back"
        wall_back.scale = (0.65, 1, 0.15)
        wall_back.rotation_euler = (1.5708, 0, 0)
        
        # Top (atmosphere)
        bpy.ops.mesh.primitive_plane_add(
            size=1,
            location=(0.45, 0, 0.3)
        )
        atmosphere = bpy.context.active_object
        atmosphere.name = "Atmosphere"
        atmosphere.scale = (0.65, 0.1, 1)
        
        # Join walls into single object
        bpy.ops.object.select_all(action='DESELECT')
        wall_front.select_set(True)
        wall_back.select_set(True)
        ground.select_set(True)  # Include ground in walls
        bpy.context.view_layer.objects.active = wall_front
        bpy.ops.object.join()
        bpy.context.active_object.name = "Wall"
        
        print("✅ Domain boundaries created:")
        print("  - Ground (seabed)")
        print("  - Inlet (wave generation)")
        print("  - Outlet (wave absorption)")
        print("  - Wall (combined boundaries)")
        print("  - Atmosphere (top boundary)")
    
    def check_mesh_quality(self):
        """Check mesh quality of all objects"""
        issues = []
        
        for obj in bpy.context.scene.objects:
            if obj.type != 'MESH':
                continue
            
            # Switch to edit mode
            bpy.context.view_layer.objects.active = obj
            bpy.ops.object.mode_set(mode='EDIT')
            
            # Create bmesh representation
            bm = bmesh.from_mesh(obj.data)
            
            # Check for non-manifold edges
            non_manifold = [e for e in bm.edges if not e.is_manifold]
            if non_manifold:
                issues.append(f"{obj.name}: {len(non_manifold)} non-manifold edges")
            
            # Check for loose vertices
            loose_verts = [v for v in bm.verts if not v.link_edges]
            if loose_verts:
                issues.append(f"{obj.name}: {len(loose_verts)} loose vertices")
            
            # Check for duplicate vertices
            bmesh.ops.remove_doubles(bm, verts=bm.verts, dist=0.0001)
            
            bm.free()
            bpy.ops.object.mode_set(mode='OBJECT')
        
        if issues:
            print("⚠️ Mesh quality issues found:")
            for issue in issues:
                print(f"  - {issue}")
        else:
            print("✅ All meshes appear to be watertight")
        
        return len(issues) == 0
    
    def prepare_for_snappyhex_export(self, case_name="coral_case"):
        """Prepare geometry for SnappyHexMesh GUI export"""
        
        print(f"🔧 Preparing geometry for SnappyHexMesh export...")
        
        # 1. Analyze and scale coral
        if not self.scale_coral_to_target():
            return False
        
        # 2. Position coral
        if not self.position_coral_at_origin():
            return False
        
        # 3. Create domain boundaries
        self.create_domain_boundaries()
        
        # 4. Check mesh quality
        self.check_mesh_quality()
        
        # 5. Set up materials/collections for easy selection
        self.organize_objects()
        
        print(f"✅ Geometry preparation complete!")
        print(f"\n📋 Next steps:")
        print(f"1. Install SnappyHexMesh GUI add-on if not already installed")
        print(f"2. Configure export settings:")
        print(f"   - Export directory: cases/{case_name}/mesh/")
        print(f"   - Background mesh: 15×4×5 cells")
        print(f"   - Refinement levels: 0-1")
        print(f"3. Select and export each component:")
        print(f"   - Coral → Coral.stl")
        print(f"   - Ground → Ground.stl") 
        print(f"   - Inlet → Inlet.stl")
        print(f"   - Outlet → Outlet.stl")
        print(f"   - Wall → Wall.stl")
        print(f"4. Export SnappyHexMesh configuration files")
        
        return True
    
    def organize_objects(self):
        """Organize objects into collections for easy management"""
        
        # Create collections
        collections = {
            'Coral': [],
            'Boundaries': ['Ground', 'Inlet', 'Outlet', 'Wall', 'Atmosphere']
        }
        
        for coll_name in collections.keys():
            if coll_name not in bpy.data.collections:
                new_collection = bpy.data.collections.new(coll_name)
                bpy.context.scene.collection.children.link(new_collection)
        
        # Move objects to appropriate collections
        for obj in bpy.context.scene.objects:
            if obj.type == 'MESH':
                if 'coral' in obj.name.lower():
                    target_coll = 'Coral'
                elif obj.name in collections['Boundaries']:
                    target_coll = 'Boundaries'
                else:
                    continue
                
                # Remove from current collections
                for coll in obj.users_collection:
                    coll.objects.unlink(obj)
                
                # Add to target collection
                bpy.data.collections[target_coll].objects.link(obj)
        
        print("✅ Objects organized into collections")

# Helper functions for direct use in Blender

def quick_coral_setup():
    """Quick setup function for coral geometry"""
    helper = CoralGeometryHelper()
    return helper.prepare_for_snappyhex_export()

def analyze_coral():
    """Quick analysis of coral geometry"""
    helper = CoralGeometryHelper()
    return helper.analyze_coral_geometry()

def check_quality():
    """Quick mesh quality check"""
    helper = CoralGeometryHelper()
    return helper.check_mesh_quality()

# Main execution
if __name__ == "__main__":
    print("🌊 Coral Reef Geometry Helper for Blender")
    print("=" * 50)
    
    # Check if running in Blender
    try:
        import bpy
        print("✅ Running in Blender environment")
        
        # Create helper instance
        helper = CoralGeometryHelper()
        
        # Show available functions
        print("\n📋 Available functions:")
        print("  helper.analyze_coral_geometry() - Analyze current coral")
        print("  helper.scale_coral_to_target() - Scale to 5cm height")
        print("  helper.position_coral_at_origin() - Position at origin")
        print("  helper.create_domain_boundaries() - Create domain")
        print("  helper.check_mesh_quality() - Check mesh quality")
        print("  helper.prepare_for_snappyhex_export() - Complete setup")
        print("\n  Quick functions:")
        print("  quick_coral_setup() - Complete automated setup")
        print("  analyze_coral() - Quick analysis")
        print("  check_quality() - Quick quality check")
        
    except ImportError:
        print("❌ Not running in Blender. This script requires Blender environment.")
        print("Usage: Run this script in Blender's Text Editor")
