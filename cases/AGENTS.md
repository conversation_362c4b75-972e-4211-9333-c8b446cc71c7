# AGENTS.md - OpenFOAM Coral Wave Energy Study

## Build/Test/Lint Commands
```bash
# Run single coral simulation
./run_wave_simulations.sh BR01  # Options: BR01, CY02, EN03, MA04, TB05

# Run all simulations
./run_wave_simulations.sh

# Analyze results
python3 analyze_wave_energy.py [case_directory]

# Python linting (if needed)
python3 -m py_compile analyze_wave_energy.py
```

## Code Style Guidelines
- **Python**: Follow PEP 8, use snake_case, 4-space indentation, docstrings for functions
- **Shell scripts**: Use bash shebang, validate exit codes, quote variables, descriptive function names
- **OpenFOAM**: Standard case structure (0/, constant/, system/), consistent naming for coral cases
- **Error handling**: Check file existence, validate data loading, provide meaningful error messages
- **Comments**: Document complex calculations, explain OpenFOAM-specific operations
- **File organization**: Keep STL files in geom/, results in postProcessing/, logs as log.{application}