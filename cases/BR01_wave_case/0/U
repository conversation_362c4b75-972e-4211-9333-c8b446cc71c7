/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2506                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       volVectorField;
    location    "0";
    object      U;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 1 -1 0 0 0 0];

internalField   uniform (0 0 0);

boundaryField
{
    inlet
    {
        type            flowRateInletVelocity;
        volumetricFlowRate  sine;
        volumetricFlowRateCoeffs
        {
            frequency       1.0;
            amplitude       0.01;  // small oscillating flow rate
            scale           1;
            level           0.02;  // base flow rate
        }
        value           uniform (0.1 0 0);
    }
    
    outlet
    {
        type            pressureInletOutletVelocity;
        value           uniform (0 0 0);
    }
    
    bottom
    {
        type            noSlip;
    }
    
    atmosphere
    {
        type            pressureInletOutletVelocity; 
        value           uniform (0 0 0);
    }
    
    front
    {
        type            noSlip;
    }
    
    back
    {
        type            noSlip;
    }
    
    coral
    {
        type            noSlip;
    }
}

// ************************************************************************* //