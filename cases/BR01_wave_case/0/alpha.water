/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2506                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       volScalarField;
    location    "0";
    object      alpha.water;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 0 0 0 0 0 0];

internalField   uniform 0.8;

boundaryField
{
    inlet
    {
        type            fixedValue;
        value           uniform 0.9;  // mostly water at inlet
    }
    
    outlet
    {
        type            zeroGradient;
    }
    
    bottom
    {
        type            zeroGradient;
    }
    
    atmosphere
    {
        type            inletOutlet;
        inletValue      uniform 0;
        value           uniform 0;
    }
    
    front
    {
        type            zeroGradient;
    }
    
    back
    {
        type            zeroGradient;
    }
    
    coral
    {
        type            zeroGradient;
    }
}

// ************************************************************************* //