/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  9                                     |
|   \\  /    A nd           | Web:      www.OpenFOAM.org                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       volScalarField;
    location    "0";
    object      k;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 2 -2 0 0 0 0];

internalField   uniform 1e-6;

boundaryField
{
    inlet
    {
        type            fixedValue;
        value           uniform 1e-6;
    }
    
    outlet
    {
        type            zeroGradient;
    }
    
    bottom
    {
        type            kqRWallFunction;
        value           uniform 1e-6;
    }
    
    atmosphere
    {
        type            inletOutlet;
        inletValue      uniform 1e-6;
        value           uniform 1e-6;
    }
    
    front
    {
        type            kqRWallFunction;
        value           uniform 1e-6;
    }
    
    back
    {
        type            kqRWallFunction;
        value           uniform 1e-6;
    }
    
    coral
    {
        type            kqRWallFunction;
        value           uniform 1e-6;
    }
}

// ************************************************************************* //