/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  9                                     |
|   \\  /    A nd           | Web:      www.OpenFOAM.org                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       volScalarField;
    location    "0";
    object      nut;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 2 -1 0 0 0 0];

internalField   uniform 1e-6;

boundaryField
{
    inlet
    {
        type            calculated;
        value           uniform 1e-6;
    }
    
    outlet
    {
        type            calculated;
        value           uniform 1e-6;
    }
    
    bottom
    {
        type            nutkWallFunction;
        value           uniform 1e-6;
    }
    
    atmosphere
    {
        type            calculated;
        value           uniform 1e-6;
    }
    
    front
    {
        type            nutkWallFunction;
        value           uniform 1e-6;
    }
    
    back
    {
        type            nutkWallFunction;
        value           uniform 1e-6;
    }
    
    coral
    {
        type            nutkWallFunction;
        value           uniform 1e-6;
    }
}

// ************************************************************************* //