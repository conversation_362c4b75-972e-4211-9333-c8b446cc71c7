/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  9                                     |
|   \\  /    A nd           | Web:      www.OpenFOAM.org                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;  
    format      ascii;
    class       volScalarField;
    location    "0";
    object      omega;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 0 -1 0 0 0 0];

internalField   uniform 1;

boundaryField
{
    inlet
    {
        type            fixedValue;
        value           uniform 1;
    }
    
    outlet
    {
        type            zeroGradient;
    }
    
    bottom
    {
        type            omegaWallFunction;
        value           uniform 1;
    }
    
    atmosphere
    {
        type            inletOutlet;
        inletValue      uniform 1;
        value           uniform 1;
    }
    
    front
    {
        type            omegaWallFunction;
        value           uniform 1;
    }
    
    back
    {
        type            omegaWallFunction;
        value           uniform 1;
    }
    
    coral
    {
        type            omegaWallFunction;
        value           uniform 1;
    }
}

// ************************************************************************* //