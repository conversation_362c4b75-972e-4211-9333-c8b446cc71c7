#!/usr/bin/env python3
"""
Analyze BR01 branching coral STL geometry
"""

import struct
import numpy as np

def read_stl_binary(filename):
    """Read binary STL file and return triangles"""
    with open(filename, 'rb') as f:
        # Skip 80-byte header
        header = f.read(80)
        print(f"STL Header: {header[:50].decode('ascii', errors='ignore')}")
        
        # Read number of triangles
        num_triangles = struct.unpack('<I', f.read(4))[0]
        print(f"Number of triangles: {num_triangles}")
        
        triangles = []
        vertices = []
        
        for i in range(num_triangles):
            # Read normal vector (3 floats)
            normal = struct.unpack('<3f', f.read(12))
            
            # Read 3 vertices (9 floats)
            v1 = struct.unpack('<3f', f.read(12))
            v2 = struct.unpack('<3f', f.read(12))
            v3 = struct.unpack('<3f', f.read(12))
            
            # Skip attribute byte count (2 bytes)
            f.read(2)
            
            vertices.extend([v1, v2, v3])
            triangles.append([normal, v1, v2, v3])
        
        return np.array(vertices), triangles, num_triangles

def analyze_geometry(vertices):
    """Analyze STL geometry bounds and dimensions"""
    vertices = np.array(vertices)
    
    min_coords = np.min(vertices, axis=0)
    max_coords = np.max(vertices, axis=0)
    center = (min_coords + max_coords) / 2
    dimensions = max_coords - min_coords
    
    print(f"\n=== BR01 Branching Coral Geometry Analysis ===")
    print(f"Bounding box:")
    print(f"  X: {min_coords[0]:.6f} to {max_coords[0]:.6f} (width: {dimensions[0]:.6f} m)")
    print(f"  Y: {min_coords[1]:.6f} to {max_coords[1]:.6f} (depth: {dimensions[1]:.6f} m)") 
    print(f"  Z: {min_coords[2]:.6f} to {max_coords[2]:.6f} (height: {dimensions[2]:.6f} m)")
    print(f"Center: ({center[0]:.6f}, {center[1]:.6f}, {center[2]:.6f})")
    print(f"Volume envelope: {dimensions[0]*dimensions[1]*dimensions[2]:.9f} m³")
    
    return {
        'min_coords': min_coords,
        'max_coords': max_coords,
        'center': center,
        'dimensions': dimensions
    }

def main():
    stl_file = "constant/triSurface/coral.stl"
    
    try:
        vertices, triangles, num_triangles = read_stl_binary(stl_file)
        geometry = analyze_geometry(vertices)
        
        print(f"\n=== Mesh Integration Check ===")
        print(f"STL file: {stl_file}")
        print(f"File size: {len(open(stl_file, 'rb').read())} bytes")
        print(f"Triangles: {num_triangles}")
        print(f"Expected for 5cm coral: ~0.05m height")
        print(f"Actual height: {geometry['dimensions'][2]:.6f} m")
        
        if geometry['dimensions'][2] > 0.04 and geometry['dimensions'][2] < 0.06:
            print("✅ Coral height looks correct for 5cm scaled model")
        else:
            print("⚠️ Coral height may need scaling adjustment")
            
        print(f"\n=== Domain Position ===")
        # Typical domain is 22.4cm x 16cm x 16cm
        domain_center_x = 0.112  # 22.4cm / 2
        coral_offset_x = geometry['center'][0]
        
        print(f"Domain center X (expected): {domain_center_x:.3f} m")
        print(f"Coral center X (actual): {coral_offset_x:.6f} m")
        print(f"Coral offset from domain center: {coral_offset_x - domain_center_x:.6f} m")
        
    except Exception as e:
        print(f"Error reading STL: {e}")

if __name__ == "__main__":
    main()