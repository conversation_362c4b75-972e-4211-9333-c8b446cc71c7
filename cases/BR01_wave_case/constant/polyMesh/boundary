/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2506                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      binary;
    arch        "LSB;label=32;scalar=64";
    class       polyBoundaryMesh;
    location    "constant/polyMesh";
    object      boundary;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

7
(
    inlet
    {
        type            patch;
        nFaces          1716;
        startFace       330488;
    }
    outlet
    {
        type            patch;
        nFaces          1716;
        startFace       332204;
    }
    front
    {
        type            wall;
        inGroups        1(wall);
        nFaces          1200;
        startFace       333920;
    }
    back
    {
        type            wall;
        inGroups        1(wall);
        nFaces          1200;
        startFace       335120;
    }
    bottom
    {
        type            wall;
        inGroups        1(wall);
        nFaces          5436;
        startFace       336320;
    }
    atmosphere
    {
        type            patch;
        nFaces          3600;
        startFace       341756;
    }
    coral
    {
        type            wall;
        inGroups        2(coral wall);
        nFaces          13429;
        startFace       345356;
    }
)

// ************************************************************************* //
