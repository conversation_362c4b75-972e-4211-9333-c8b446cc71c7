
LONGITUDINAL CROSS-SECTIONAL CONTOUR ANALYSIS
=============================================

Case: BR01_wave_case

AVAILABLE FIELD DATA FOR VISUALIZATION:

Latest time step: 0 s
Available fields:
  - alpha.water: Water volume fraction (0=air, 1=water)
  - k: Turbulent kinetic energy [m²/s²]
  - nut: Turbulent viscosity [m²/s]
  - omega: Specific dissipation rate [1/s]
  - p_rgh: Hydrostatic pressure [Pa]
  - U: Velocity vector field [m/s]


VISUALIZATION OPTIONS:
=====================

1. ParaView GUI (Recommended):
   paraFoam -case BR01_wave_case
   
   Steps for longitudinal cross-section:
   a) Load case and go to latest time step
   b) Create slice filter (Filters → Alphabetical → Slice)
   c) Set slice normal to [1,0,0] (X-direction)
   d) Position slice at x=0.2 (domain center)
   e) Color by desired field (alpha.water, U magnitude, p_rgh, k)

2. Automated Python visualization:
   pvpython BR01_wave_case/generate_plots.py

3. Manual slice extraction:
   sample -case BR01_wave_case -time 0

EXPECTED CONTOUR FEATURES:
=========================
- Wave propagation pattern in alpha.water field
- Velocity amplification/reduction around coral
- Pressure variations due to flow obstruction  
- Turbulent kinetic energy peaks in coral wake
- Energy dissipation zones downstream of coral

ANALYSIS INTERPRETATION:
=======================
- High alpha.water values (blue) = water regions
- Low alpha.water values (red) = air/wave troughs
- Velocity magnitude shows flow acceleration/deceleration
- Pressure contours indicate wave loading on coral
- Turbulent k.e. quantifies energy dissipation effectiveness
