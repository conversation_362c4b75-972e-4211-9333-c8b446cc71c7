#!/usr/bin/env python3
"""
Text-based analysis of the BR01 coral setup
"""

import struct
import numpy as np

def read_stl_binary(filename):
    """Read binary STL file"""
    with open(filename, 'rb') as f:
        header = f.read(80)
        num_triangles = struct.unpack('<I', f.read(4))[0]
        
        vertices = []
        
        for i in range(num_triangles):
            f.read(12)  # Skip normal
            v1 = struct.unpack('<3f', f.read(12))
            v2 = struct.unpack('<3f', f.read(12))
            v3 = struct.unpack('<3f', f.read(12))
            vertices.extend([v1, v2, v3])
            f.read(2)   # Skip attributes
        
        return np.array(vertices), num_triangles

def main():
    print("=" * 60)
    print("🌊 BR01 BRANCHING CORAL - SIMULATION READY ANALYSIS")
    print("=" * 60)
    
    # Read coral geometry
    vertices, num_triangles = read_stl_binary("constant/triSurface/coral.stl")
    
    # Calculate bounds and dimensions
    bounds = {
        'x_min': np.min(vertices[:, 0]), 'x_max': np.max(vertices[:, 0]),
        'y_min': np.min(vertices[:, 1]), 'y_max': np.max(vertices[:, 1]),
        'z_min': np.min(vertices[:, 2]), 'z_max': np.max(vertices[:, 2])
    }
    
    dims = {
        'width': bounds['x_max'] - bounds['x_min'],
        'depth': bounds['y_max'] - bounds['y_min'], 
        'height': bounds['z_max'] - bounds['z_min']
    }
    
    center = {
        'x': (bounds['x_min'] + bounds['x_max']) / 2,
        'y': (bounds['y_min'] + bounds['y_max']) / 2,
        'z': (bounds['z_min'] + bounds['z_max']) / 2
    }
    
    print(f"\n📐 CORAL GEOMETRY:")
    print(f"   Bounding Box:")
    print(f"     X: {bounds['x_min']:8.5f} to {bounds['x_max']:8.5f} m")
    print(f"     Y: {bounds['y_min']:8.5f} to {bounds['y_max']:8.5f} m") 
    print(f"     Z: {bounds['z_min']:8.5f} to {bounds['z_max']:8.5f} m")
    
    print(f"\n   Dimensions:")
    print(f"     Width (X):  {dims['width']:8.5f} m ({dims['width']*100:5.1f} cm)")
    print(f"     Depth (Y):  {dims['depth']:8.5f} m ({dims['depth']*100:5.1f} cm)")
    print(f"     Height (Z): {dims['height']:8.5f} m ({dims['height']*100:5.1f} cm)")
    
    print(f"\n   Center Position:")
    print(f"     X: {center['x']:8.5f} m")
    print(f"     Y: {center['y']:8.5f} m")
    print(f"     Z: {center['z']:8.5f} m")
    
    print(f"\n🗂️ DOMAIN COMPATIBILITY:")
    domain_size = 0.224  # 22.4cm
    domain_height = 0.160  # 16cm
    
    print(f"   Domain Size: {domain_size*100:.1f} × {domain_size*100:.1f} × {domain_height*100:.1f} cm")
    print(f"   Coral Size:  {dims['width']*100:.1f} × {dims['depth']*100:.1f} × {dims['height']*100:.1f} cm")
    
    x_fit = dims['width'] < domain_size
    y_fit = dims['depth'] < domain_size  
    z_fit = dims['height'] < domain_height
    centered_x = abs(center['x']) < 0.01
    centered_y = abs(center['y']) < 0.01
    
    print(f"   X-direction: {'✅ FITS' if x_fit else '❌ TOO LARGE'} ({'✅ CENTERED' if centered_x else '❌ OFF-CENTER'})")
    print(f"   Y-direction: {'✅ FITS' if y_fit else '❌ TOO LARGE'} ({'✅ CENTERED' if centered_y else '❌ OFF-CENTER'})")
    print(f"   Z-direction: {'✅ FITS' if z_fit else '❌ TOO TALL'}")
    
    print(f"\n🌊 WAVE ENVIRONMENT:")
    wave_height = 0.16
    water_depth = 0.16
    
    print(f"   Wave Height:    {wave_height*100:5.1f} cm")
    print(f"   Water Depth:    {water_depth*100:5.1f} cm")
    print(f"   Coral Height:   {dims['height']*100:5.1f} cm")
    print(f"   Height Ratio:   {(dims['height']/water_depth)*100:5.1f}% of water depth")
    print(f"   Wave/Coral:     {(wave_height/dims['height']):5.1f}× wave to coral ratio")
    
    submersion_ratio = (bounds['z_max'] / water_depth) * 100
    if submersion_ratio < 100:
        print(f"   Submersion:     {submersion_ratio:5.1f}% submerged (✅ realistic)")
    else:
        print(f"   Submersion:     {submersion_ratio:5.1f}% extends above water (❌ issue)")
    
    print(f"\n📊 STL FILE DETAILS:")
    file_size = len(open("constant/triSurface/coral.stl", 'rb').read())
    print(f"   File Size:      {file_size:,} bytes ({file_size/1024:.1f} KB)")
    print(f"   Triangles:      {num_triangles:,}")
    print(f"   Vertices:       {len(vertices):,}")
    print(f"   Resolution:     {num_triangles/(dims['width']*dims['depth']):.0f} triangles/m²")
    
    print(f"\n✅ SIMULATION READINESS:")
    all_fits = x_fit and y_fit and z_fit
    all_centered = centered_x and centered_y
    realistic_size = 0.04 < dims['height'] < 0.06  # 4-6cm realistic
    
    print(f"   Geometry Fits:  {'✅ YES' if all_fits else '❌ NO'}")
    print(f"   Well Centered:  {'✅ YES' if all_centered else '❌ NO'}")  
    print(f"   Realistic Size: {'✅ YES' if realistic_size else '❌ NO'}")
    print(f"   File Valid:     {'✅ YES' if num_triangles > 1000 else '❌ NO'}")
    
    overall_ready = all_fits and all_centered and realistic_size and num_triangles > 1000
    print(f"\n🎯 OVERALL STATUS: {'✅ READY FOR SIMULATION' if overall_ready else '⚠️ NEEDS ADJUSTMENT'}")
    
    if overall_ready:
        print(f"\n🚀 NEXT STEPS:")
        print(f"   1. blockMesh      → Generate background mesh")
        print(f"   2. snappyHexMesh  → Integrate coral geometry")  
        print(f"   3. setFields      → Initialize wave conditions")
        print(f"   4. interFoam      → Run wave simulation")
        print(f"   5. Analysis       → Extract force coefficients")
        
        print(f"\n📈 EXPECTED RESULTS:")
        print(f"   • Wave energy reduction: 40-60% (branching coral)")
        print(f"   • Complex turbulent flow around branches")
        print(f"   • Drag coefficient: 10-20 (high surface area)")
        print(f"   • Flow separation and vortex shedding")
    
    print("=" * 60)

if __name__ == "__main__":
    main()