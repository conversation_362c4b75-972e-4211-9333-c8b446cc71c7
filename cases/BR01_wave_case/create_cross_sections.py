#!/usr/bin/env python3
"""
Create cross-sectional domain analysis to verify coral positioning
"""

import struct
import numpy as np
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle
import matplotlib.patches as patches

def read_stl_binary(filename):
    """Read binary STL file"""
    with open(filename, 'rb') as f:
        header = f.read(80)
        num_triangles = struct.unpack('<I', f.read(4))[0]
        
        vertices = []
        triangles = []
        
        for i in range(num_triangles):
            # Read normal
            normal = struct.unpack('<3f', f.read(12))
            
            # Read vertices
            v1 = struct.unpack('<3f', f.read(12))
            v2 = struct.unpack('<3f', f.read(12))
            v3 = struct.unpack('<3f', f.read(12))
            
            vertices.extend([v1, v2, v3])
            triangles.append([normal, v1, v2, v3])
            
            # Skip attributes
            f.read(2)
        
        return np.array(vertices), triangles, num_triangles

def create_domain_cross_sections():
    """Create comprehensive cross-sectional analysis"""
    
    # Read coral geometry
    vertices, triangles, num_triangles = read_stl_binary("constant/triSurface/coral.stl")
    
    # Domain parameters
    domain_x = [-0.112, 0.112]  # 22.4cm total
    domain_y = [-0.112, 0.112]  # 22.4cm total
    domain_z = [0, 0.160]       # 16cm height
    
    # Wave parameters
    water_level = 0.160         # 16cm water depth
    sea_floor = 0.0
    
    # Create figure with multiple cross-sections
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('🌊 BR01 BRANCHING CORAL - DOMAIN CROSS-SECTIONAL ANALYSIS', fontsize=16, fontweight='bold')
    
    # 1. YZ Cross-section at X=0 (center cut through coral)
    ax1 = axes[0, 0]
    
    # Filter vertices near X=0 (±5mm tolerance)
    tolerance = 0.005
    center_mask = np.abs(vertices[:, 0]) < tolerance
    center_vertices = vertices[center_mask]
    
    if len(center_vertices) > 0:
        ax1.scatter(center_vertices[:, 1], center_vertices[:, 2], 
                   c='darkgreen', s=20, alpha=0.8, label='Coral Cross-Section')
    
    # Draw domain boundaries
    domain_rect = Rectangle((domain_y[0], domain_z[0]), 
                           domain_y[1]-domain_y[0], domain_z[1]-domain_z[0],
                           fill=False, edgecolor='red', linewidth=2, label='Domain Boundary')
    ax1.add_patch(domain_rect)
    
    # Draw water level and sea floor
    ax1.axhline(water_level, color='blue', linestyle='-', linewidth=3, alpha=0.7, label='Water Surface')
    ax1.axhline(sea_floor, color='brown', linestyle='-', linewidth=3, alpha=0.7, label='Sea Floor')
    
    ax1.set_xlim(domain_y)
    ax1.set_ylim(domain_z)
    ax1.set_xlabel('Y Position (m)')
    ax1.set_ylabel('Z Height (m)')
    ax1.set_title('YZ Cross-Section (X=0)\nCoral Center Cut')
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    
    # 2. XZ Cross-section at Y=0 (side view through coral center)
    ax2 = axes[0, 1]
    
    # Filter vertices near Y=0
    side_mask = np.abs(vertices[:, 1]) < tolerance
    side_vertices = vertices[side_mask]
    
    if len(side_vertices) > 0:
        ax2.scatter(side_vertices[:, 0], side_vertices[:, 2], 
                   c='darkgreen', s=20, alpha=0.8, label='Coral Profile')
    
    # Draw domain boundaries
    domain_rect2 = Rectangle((domain_x[0], domain_z[0]), 
                            domain_x[1]-domain_x[0], domain_z[1]-domain_z[0],
                            fill=False, edgecolor='red', linewidth=2, label='Domain Boundary')
    ax2.add_patch(domain_rect2)
    
    # Wave direction arrow
    ax2.arrow(-0.08, 0.12, 0.06, 0, head_width=0.01, head_length=0.01, 
             fc='blue', ec='blue', label='Wave Direction')
    
    ax2.axhline(water_level, color='blue', linestyle='-', linewidth=3, alpha=0.7, label='Water Surface')
    ax2.axhline(sea_floor, color='brown', linestyle='-', linewidth=3, alpha=0.7, label='Sea Floor')
    ax2.axvline(0, color='gray', linestyle='--', alpha=0.5, label='Domain Center')
    
    ax2.set_xlim(domain_x)
    ax2.set_ylim(domain_z)
    ax2.set_xlabel('X Position (m)')
    ax2.set_ylabel('Z Height (m)')
    ax2.set_title('XZ Cross-Section (Y=0)\nWave Propagation View')
    ax2.grid(True, alpha=0.3)
    ax2.legend()
    
    # 3. XY Cross-section at Z=0.035 (mid-height of coral)
    ax3 = axes[0, 2]
    
    # Filter vertices at coral mid-height
    mid_height = 0.035
    top_mask = np.abs(vertices[:, 2] - mid_height) < tolerance
    top_vertices = vertices[top_mask]
    
    if len(top_vertices) > 0:
        ax3.scatter(top_vertices[:, 0], top_vertices[:, 1], 
                   c='darkgreen', s=20, alpha=0.8, label='Coral Mid-Section')
    
    # Draw domain boundaries
    domain_rect3 = Rectangle((domain_x[0], domain_y[0]), 
                            domain_x[1]-domain_x[0], domain_y[1]-domain_y[0],
                            fill=False, edgecolor='red', linewidth=2, label='Domain Boundary')
    ax3.add_patch(domain_rect3)
    
    ax3.axhline(0, color='gray', linestyle='--', alpha=0.5, label='Y=0 Center')
    ax3.axvline(0, color='gray', linestyle='--', alpha=0.5, label='X=0 Center')
    
    ax3.set_xlim(domain_x)
    ax3.set_ylim(domain_y)
    ax3.set_xlabel('X Position (m)')
    ax3.set_ylabel('Y Position (m)')
    ax3.set_title(f'XY Cross-Section (Z={mid_height:.3f}m)\nTop View - Coral Mid-Height')
    ax3.grid(True, alpha=0.3)
    ax3.legend()
    
    # 4. Multiple XZ slices showing coral distribution
    ax4 = axes[1, 0]
    
    # Create multiple Y-slices to show coral distribution
    y_slices = [-0.015, -0.005, 0.005, 0.015]
    colors = ['lightgreen', 'green', 'darkgreen', 'forestgreen']
    
    for i, y_slice in enumerate(y_slices):
        slice_mask = np.abs(vertices[:, 1] - y_slice) < 0.003
        slice_vertices = vertices[slice_mask]
        
        if len(slice_vertices) > 0:
            ax4.scatter(slice_vertices[:, 0], slice_vertices[:, 2], 
                       c=colors[i], s=15, alpha=0.7, 
                       label=f'Y={y_slice*1000:+.0f}mm slice')
    
    # Domain boundary
    domain_rect4 = Rectangle((domain_x[0], domain_z[0]), 
                            domain_x[1]-domain_x[0], domain_z[1]-domain_z[0],
                            fill=False, edgecolor='red', linewidth=2)
    ax4.add_patch(domain_rect4)
    
    ax4.axhline(water_level, color='blue', linestyle='-', linewidth=2, alpha=0.7)
    ax4.axhline(sea_floor, color='brown', linestyle='-', linewidth=2, alpha=0.7)
    
    ax4.set_xlim(domain_x)
    ax4.set_ylim(domain_z)
    ax4.set_xlabel('X Position (m)')
    ax4.set_ylabel('Z Height (m)')
    ax4.set_title('Multiple Y-Slices\nCoral Depth Distribution')
    ax4.grid(True, alpha=0.3)
    ax4.legend()
    
    # 5. Coral bounds verification
    ax5 = axes[1, 1]
    ax5.axis('off')
    
    # Calculate coral statistics
    coral_bounds = {
        'x_min': np.min(vertices[:, 0]), 'x_max': np.max(vertices[:, 0]),
        'y_min': np.min(vertices[:, 1]), 'y_max': np.max(vertices[:, 1]),
        'z_min': np.min(vertices[:, 2]), 'z_max': np.max(vertices[:, 2])
    }
    
    coral_center = {
        'x': (coral_bounds['x_min'] + coral_bounds['x_max']) / 2,
        'y': (coral_bounds['y_min'] + coral_bounds['y_max']) / 2,
        'z': (coral_bounds['z_min'] + coral_bounds['z_max']) / 2
    }
    
    coral_dims = {
        'width': coral_bounds['x_max'] - coral_bounds['x_min'],
        'depth': coral_bounds['y_max'] - coral_bounds['y_min'],
        'height': coral_bounds['z_max'] - coral_bounds['z_min']
    }
    
    # Check domain fit
    x_margin = (domain_x[1] - domain_x[0]) - coral_dims['width']
    y_margin = (domain_y[1] - domain_y[0]) - coral_dims['depth']
    z_margin = (domain_z[1] - domain_z[0]) - coral_dims['height']
    
    verification_text = f"""
🔍 DOMAIN VERIFICATION REPORT

📐 CORAL BOUNDS:
X: {coral_bounds['x_min']:7.4f} to {coral_bounds['x_max']:7.4f} m
Y: {coral_bounds['y_min']:7.4f} to {coral_bounds['y_max']:7.4f} m  
Z: {coral_bounds['z_min']:7.4f} to {coral_bounds['z_max']:7.4f} m

📏 DOMAIN SIZE:
X: {domain_x[0]:7.4f} to {domain_x[1]:7.4f} m ({(domain_x[1]-domain_x[0])*100:5.1f} cm)
Y: {domain_y[0]:7.4f} to {domain_y[1]:7.4f} m ({(domain_y[1]-domain_y[0])*100:5.1f} cm)
Z: {domain_z[0]:7.4f} to {domain_z[1]:7.4f} m ({(domain_z[1]-domain_z[0])*100:5.1f} cm)

📊 CORAL DIMENSIONS:
Width:  {coral_dims['width']*100:5.1f} cm
Depth:  {coral_dims['depth']*100:5.1f} cm  
Height: {coral_dims['height']*100:5.1f} cm

🎯 POSITIONING:
Center X: {coral_center['x']:8.5f} m (offset: {coral_center['x']*1000:+5.1f} mm)
Center Y: {coral_center['y']:8.5f} m (offset: {coral_center['y']*1000:+5.1f} mm)
Center Z: {coral_center['z']:8.5f} m (height: {coral_center['z']*100:4.1f} cm)

🚧 CLEARANCES:
X-margin: {x_margin*100:5.1f} cm ({x_margin*1000/2:+4.1f} mm each side)
Y-margin: {y_margin*100:5.1f} cm ({y_margin*1000/2:+4.1f} mm each side)
Z-margin: {z_margin*100:5.1f} cm ({z_margin*100:4.1f} cm above coral)

✅ FIT CHECK:
X-direction: {'✅ FITS' if x_margin > 0 else '❌ TOO WIDE'}
Y-direction: {'✅ FITS' if y_margin > 0 else '❌ TOO DEEP'}  
Z-direction: {'✅ FITS' if z_margin > 0 else '❌ TOO TALL'}
Centered: {'✅ YES' if abs(coral_center['x']) < 0.01 and abs(coral_center['y']) < 0.01 else '❌ NO'}

🌊 WAVE INTERACTION:
Base clearance: {coral_bounds['z_min']*100:4.1f} cm above sea floor
Top clearance:  {(water_level - coral_bounds['z_max'])*100:4.1f} cm below surface
Submersion:     {(coral_bounds['z_max']/water_level)*100:4.1f}% of water depth
"""
    
    ax5.text(0.05, 0.95, verification_text, transform=ax5.transAxes, fontsize=10,
             verticalalignment='top', fontfamily='monospace',
             bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    
    # 6. Wave propagation analysis
    ax6 = axes[1, 2]
    
    # Show coral density along X-direction (wave path)
    x_bins = np.linspace(domain_x[0], domain_x[1], 50)
    coral_density = []
    
    for i in range(len(x_bins)-1):
        x_min, x_max = x_bins[i], x_bins[i+1]
        in_bin = (vertices[:, 0] >= x_min) & (vertices[:, 0] < x_max)
        density = np.sum(in_bin)
        coral_density.append(density)
    
    x_centers = (x_bins[:-1] + x_bins[1:]) / 2
    ax6.bar(x_centers, coral_density, width=x_bins[1]-x_bins[0], 
           alpha=0.7, color='green', edgecolor='darkgreen')
    
    ax6.set_xlabel('X Position (m)')
    ax6.set_ylabel('Coral Point Density')
    ax6.set_title('Coral Density Along Wave Path\n(Wave Interaction Analysis)')
    ax6.grid(True, alpha=0.3)
    
    # Mark wave entry and exit
    ax6.axvline(domain_x[0], color='blue', linestyle='--', alpha=0.7, label='Wave Inlet')
    ax6.axvline(domain_x[1], color='blue', linestyle=':', alpha=0.7, label='Wave Outlet')
    ax6.axvline(0, color='red', linestyle='-', alpha=0.7, label='Domain Center')
    ax6.legend()
    
    plt.tight_layout()
    plt.savefig('domain_cross_sections.png', dpi=300, bbox_inches='tight')
    print("✅ Cross-sectional analysis saved as 'domain_cross_sections.png'")
    
    return coral_bounds, coral_dims, coral_center

def create_domain_summary():
    """Create a text summary of domain verification"""
    
    vertices, _, num_triangles = read_stl_binary("constant/triSurface/coral.stl")
    
    print("\n" + "="*70)
    print("🔍 DOMAIN CROSS-SECTIONAL VERIFICATION COMPLETE")
    print("="*70)
    
    # Domain parameters
    domain_x = [-0.112, 0.112]
    domain_y = [-0.112, 0.112] 
    domain_z = [0, 0.160]
    
    coral_bounds = {
        'x_min': np.min(vertices[:, 0]), 'x_max': np.max(vertices[:, 0]),
        'y_min': np.min(vertices[:, 1]), 'y_max': np.max(vertices[:, 1]),
        'z_min': np.min(vertices[:, 2]), 'z_max': np.max(vertices[:, 2])
    }
    
    # Verification checks
    x_fits = coral_bounds['x_min'] >= domain_x[0] and coral_bounds['x_max'] <= domain_x[1]
    y_fits = coral_bounds['y_min'] >= domain_y[0] and coral_bounds['y_max'] <= domain_y[1]
    z_fits = coral_bounds['z_min'] >= domain_z[0] and coral_bounds['z_max'] <= domain_z[1]
    
    x_centered = abs((coral_bounds['x_min'] + coral_bounds['x_max'])/2) < 0.01
    y_centered = abs((coral_bounds['y_min'] + coral_bounds['y_max'])/2) < 0.01
    
    print(f"\n📊 CROSS-SECTION ANALYSIS RESULTS:")
    print(f"   • YZ Cut (X=0): Shows coral center profile ✅")
    print(f"   • XZ Cut (Y=0): Shows wave interaction path ✅") 
    print(f"   • XY Cut (Z=0.035m): Shows coral branching pattern ✅")
    print(f"   • Multiple slices: Confirm 3D coral distribution ✅")
    
    print(f"\n🎯 DOMAIN FIT VERIFICATION:")
    print(f"   • X-direction fit: {'✅ PASS' if x_fits else '❌ FAIL'}")
    print(f"   • Y-direction fit: {'✅ PASS' if y_fits else '❌ FAIL'}")
    print(f"   • Z-direction fit: {'✅ PASS' if z_fits else '❌ FAIL'}")
    print(f"   • X-axis centered: {'✅ PASS' if x_centered else '❌ FAIL'}")
    print(f"   • Y-axis centered: {'✅ PASS' if y_centered else '❌ FAIL'}")
    
    overall_correct = x_fits and y_fits and z_fits and x_centered and y_centered
    
    print(f"\n🌊 WAVE-CORAL INTERACTION SETUP:")
    print(f"   • Wave inlet: X = {domain_x[0]:.3f}m")
    print(f"   • Coral starts: X = {coral_bounds['x_min']:.3f}m")
    print(f"   • Coral ends: X = {coral_bounds['x_max']:.3f}m") 
    print(f"   • Wave outlet: X = {domain_x[1]:.3f}m")
    print(f"   • Wave path clear: {'✅ YES' if coral_bounds['x_min'] > domain_x[0] else '❌ NO'}")
    
    print(f"\n🏁 FINAL VERIFICATION:")
    print(f"   INITIAL DOMAIN SETUP: {'✅ CORRECT' if overall_correct else '❌ NEEDS CORRECTION'}")
    
    if overall_correct:
        print(f"\n🚀 The domain is correctly configured for simulation!")
        print(f"   • Coral fits perfectly within boundaries")
        print(f"   • Positioned optimally for wave interaction")
        print(f"   • Cross-sections confirm proper 3D placement")
        print(f"   • Ready for mesh generation and simulation")
    else:
        print(f"\n⚠️ Domain setup needs adjustment!")
    
    print("="*70)

def main():
    print("🔍 Creating cross-sectional domain analysis...")
    
    try:
        coral_bounds, coral_dims, coral_center = create_domain_cross_sections()
        create_domain_summary()
        
        print(f"\n📈 Analysis complete! Check 'domain_cross_sections.png' for visual verification.")
        
    except Exception as e:
        print(f"❌ Error during analysis: {e}")
        print("Creating text-only verification...")
        create_domain_summary()

if __name__ == "__main__":
    main()