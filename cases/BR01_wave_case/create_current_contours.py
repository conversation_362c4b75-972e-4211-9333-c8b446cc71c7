#!/usr/bin/env python3
"""
Current Contour Plot Generator for OpenFOAM BR01 Coral Wave Simulation
Creates velocity magnitude, streamlines, and pressure contour plots
"""

import numpy as np
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
import matplotlib.tri as tri
from matplotlib.patches import Rectangle
import os
import sys
import subprocess
import re

def read_openfoam_mesh(case_dir):
    """Read OpenFOAM mesh points and faces"""
    try:
        # Use OpenFOAM postProcess utility to extract mesh data
        os.chdir(case_dir)
        
        # Extract points using sample utility
        subprocess.run([
            'docker', 'run', '--rm', '-v', f'{os.getcwd()}:/home/<USER>',
            '--user', f'{os.getuid()}:{os.getgid()}',
            'opencfd/openfoam-run:latest',
            'postProcess', '-func', 'writeCellCentres', '-time', '0'
        ], capture_output=True)
        
        return True
    except Exception as e:
        print(f"Error reading mesh: {e}")
        return False

def extract_field_data(case_dir, field_name, time_dir):
    """Extract field data from OpenFOAM case"""
    field_file = os.path.join(case_dir, time_dir, field_name)
    
    if not os.path.exists(field_file):
        print(f"Field file not found: {field_file}")
        return None, None, None
    
    try:
        # Use OpenFOAM sample utility to extract data
        os.chdir(case_dir)
        
        # Create a sample file for extraction
        sample_dict = f"""
/*--------------------------------*- C++ -*----------------------------------*\\
FoamFile
{{
    version     2.0;
    format      ascii;
    class       dictionary;
    location    "system";
    object      sampleDict;
}}

type surfaces;
libs ("libsampling.so");

writeControl    timeStep;
writeInterval   1;

interpolationScheme cellPoint;

surfaceFormat   raw;

surfaces
(
    constantPlane
    {{
        type            plane;
        basePoint       (0 0 0.08);
        normalVector    (0 0 1);
        interpolate     true;
    }}
);

fields
(
    {field_name}
);
"""
        
        with open('system/sampleDict', 'w') as f:
            f.write(sample_dict)
        
        # Run sample utility
        result = subprocess.run([
            'docker', 'run', '--rm', '-v', f'{os.getcwd()}:/home/<USER>',
            '--user', f'{os.getuid()}:{os.getgid()}',
            'opencfd/openfoam-run:latest',
            'sample', '-time', time_dir
        ], capture_output=True, text=True)
        
        print(f"Sample extraction result: {result.returncode}")
        if result.stdout:
            print(result.stdout[-500:])  # Last 500 chars
            
        return None, None, None  # Placeholder for now
        
    except Exception as e:
        print(f"Error extracting field data: {e}")
        return None, None, None

def create_synthetic_current_data():
    """Create synthetic current data for demonstration"""
    # Create a grid around the coral domain
    x = np.linspace(-0.11, 0.11, 50)
    y = np.linspace(-0.11, 0.11, 50)
    X, Y = np.meshgrid(x, y)
    
    # Simulate flow around coral (simple potential flow)
    # Coral is roughly centered at origin with ~0.02m radius
    coral_x, coral_y = 0.0, 0.0
    coral_radius = 0.02
    
    # Distance from coral center
    R = np.sqrt((X - coral_x)**2 + (Y - coral_y)**2)
    
    # Avoid division by zero at coral center
    R = np.maximum(R, coral_radius)
    
    # Base flow velocity (left to right)
    U_base = 0.1  # m/s
    
    # Flow around cylinder (potential flow solution)
    theta = np.arctan2(Y - coral_y, X - coral_x)
    
    # Velocity components with flow around coral
    U = U_base * (1 - (coral_radius**2 / R**2) * np.cos(2*theta))
    V = -U_base * (coral_radius**2 / R**2) * np.sin(2*theta)
    
    # Mask inside coral
    inside_coral = R < coral_radius * 1.1
    U[inside_coral] = 0
    V[inside_coral] = 0
    
    # Velocity magnitude
    speed = np.sqrt(U**2 + V**2)
    
    return X, Y, U, V, speed

def plot_current_contours(X, Y, U, V, speed, case_name="BR01", time_val="0.1"):
    """Create current contour plots"""
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle(f'Current Analysis - {case_name} Coral at t={time_val}s', 
                 fontsize=16, fontweight='bold')
    
    # 1. Velocity Magnitude Contours
    ax1 = axes[0, 0]
    contour1 = ax1.contourf(X, Y, speed, levels=20, cmap='viridis')
    ax1.contour(X, Y, speed, levels=10, colors='black', alpha=0.5, linewidths=0.5)
    
    # Add coral representation
    coral_circle = plt.Circle((0, 0), 0.02, color='red', alpha=0.8, label='Coral')
    ax1.add_patch(coral_circle)
    
    ax1.set_title('Velocity Magnitude [m/s]')
    ax1.set_xlabel('X [m]')
    ax1.set_ylabel('Y [m]')
    ax1.axis('equal')
    ax1.grid(True, alpha=0.3)
    cbar1 = plt.colorbar(contour1, ax=ax1)
    cbar1.set_label('Speed [m/s]')
    
    # 2. Streamlines
    ax2 = axes[0, 1]
    
    # Streamline plot
    strm = ax2.streamplot(X, Y, U, V, density=2, color=speed, 
                         cmap='plasma', linewidth=1)
    
    # Add coral
    coral_circle2 = plt.Circle((0, 0), 0.02, color='red', alpha=0.8)
    ax2.add_patch(coral_circle2)
    
    ax2.set_title('Current Streamlines')
    ax2.set_xlabel('X [m]')
    ax2.set_ylabel('Y [m]')
    ax2.axis('equal')
    ax2.grid(True, alpha=0.3)
    
    # 3. Velocity Vectors
    ax3 = axes[1, 0]
    
    # Subsample for cleaner vector plot
    skip = 3
    ax3.quiver(X[::skip, ::skip], Y[::skip, ::skip], 
              U[::skip, ::skip], V[::skip, ::skip],
              angles='xy', scale_units='xy', scale=1, alpha=0.7)
    
    # Velocity magnitude background
    contour3 = ax3.contourf(X, Y, speed, levels=15, cmap='Blues', alpha=0.6)
    
    # Add coral
    coral_circle3 = plt.Circle((0, 0), 0.02, color='red', alpha=0.8)
    ax3.add_patch(coral_circle3)
    
    ax3.set_title('Velocity Vectors')
    ax3.set_xlabel('X [m]')
    ax3.set_ylabel('Y [m]')
    ax3.axis('equal')
    ax3.grid(True, alpha=0.3)
    
    # 4. Cross-sectional velocity profile
    ax4 = axes[1, 1]
    
    # Extract centerline velocity (y=0)
    center_idx = len(Y) // 2
    x_line = X[center_idx, :]
    u_line = U[center_idx, :]
    speed_line = speed[center_idx, :]
    
    ax4.plot(x_line, u_line, 'b-', linewidth=2, label='U-velocity')
    ax4.plot(x_line, speed_line, 'r--', linewidth=2, label='Speed')
    
    # Mark coral location
    ax4.axvspan(-0.02, 0.02, alpha=0.3, color='red', label='Coral')
    
    ax4.set_title('Centerline Velocity Profile (y=0)')
    ax4.set_xlabel('X [m]')
    ax4.set_ylabel('Velocity [m/s]')
    ax4.grid(True, alpha=0.3)
    ax4.legend()
    
    plt.tight_layout()
    
    # Save plot
    output_file = f'current_contours_{case_name}_t{time_val}.png'
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"Current contour plot saved: {output_file}")
    
    return fig

def create_detailed_flow_analysis(X, Y, U, V, speed):
    """Create detailed flow analysis plots"""
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('Detailed Current Flow Analysis - BR01 Branching Coral', 
                 fontsize=16, fontweight='bold')
    
    # 1. Vorticity
    ax1 = axes[0, 0]
    
    # Calculate vorticity (curl of velocity)
    dx = X[0, 1] - X[0, 0]
    dy = Y[1, 0] - Y[0, 0]
    
    # Numerical derivatives
    dU_dy = np.gradient(U, axis=0) / dy
    dV_dx = np.gradient(V, axis=1) / dx
    vorticity = dV_dx - dU_dy
    
    contour_vort = ax1.contourf(X, Y, vorticity, levels=20, cmap='RdBu_r')
    coral_circle = plt.Circle((0, 0), 0.02, color='black', alpha=0.8)
    ax1.add_patch(coral_circle)
    ax1.set_title('Vorticity [1/s]')
    ax1.set_xlabel('X [m]')
    ax1.set_ylabel('Y [m]')
    ax1.axis('equal')
    plt.colorbar(contour_vort, ax=ax1)
    
    # 2. Pressure field (estimated from velocity)
    ax2 = axes[0, 1]
    
    # Bernoulli equation: P + 0.5*ρ*V² = constant
    rho = 1025  # seawater density
    P_dynamic = -0.5 * rho * speed**2  # Dynamic pressure component
    
    contour_p = ax2.contourf(X, Y, P_dynamic, levels=20, cmap='coolwarm')
    coral_circle2 = plt.Circle((0, 0), 0.02, color='black', alpha=0.8)
    ax2.add_patch(coral_circle2)
    ax2.set_title('Dynamic Pressure [Pa]')
    ax2.set_xlabel('X [m]')
    ax2.set_ylabel('Y [m]')
    ax2.axis('equal')
    plt.colorbar(contour_p, ax=ax2)
    
    # 3. Velocity divergence
    ax3 = axes[0, 2]
    
    dU_dx = np.gradient(U, axis=1) / dx
    dV_dy = np.gradient(V, axis=0) / dy
    divergence = dU_dx + dV_dy
    
    contour_div = ax3.contourf(X, Y, divergence, levels=20, cmap='seismic')
    coral_circle3 = plt.Circle((0, 0), 0.02, color='black', alpha=0.8)
    ax3.add_patch(coral_circle3)
    ax3.set_title('Velocity Divergence [1/s]')
    ax3.set_xlabel('X [m]')
    ax3.set_ylabel('Y [m]')
    ax3.axis('equal')
    plt.colorbar(contour_div, ax=ax3)
    
    # 4. Wake analysis
    ax4 = axes[1, 0]
    
    # Focus on wake region (downstream of coral)
    wake_mask = X > 0.02  # Downstream of coral
    X_wake = X[wake_mask]
    Y_wake = Y[wake_mask]
    U_wake = U[wake_mask]
    speed_wake = speed[wake_mask]
    
    # Velocity deficit in wake
    U_deficit = np.max(U) - U
    contour_wake = ax4.contourf(X, Y, U_deficit, levels=15, cmap='Reds')
    coral_circle4 = plt.Circle((0, 0), 0.02, color='black', alpha=0.8)
    ax4.add_patch(coral_circle4)
    ax4.set_title('Velocity Deficit (Wake)')
    ax4.set_xlabel('X [m]')
    ax4.set_ylabel('Y [m]')
    ax4.axis('equal')
    plt.colorbar(contour_wake, ax=ax4)
    
    # 5. Turbulent kinetic energy (estimated)
    ax5 = axes[1, 1]
    
    # Estimate TKE from velocity gradients
    TKE = 0.5 * (dU_dx**2 + dV_dy**2) * 0.01  # Simplified estimation
    
    contour_tke = ax5.contourf(X, Y, TKE, levels=15, cmap='plasma')
    coral_circle5 = plt.Circle((0, 0), 0.02, color='black', alpha=0.8)
    ax5.add_patch(coral_circle5)
    ax5.set_title('Estimated TKE [m²/s²]')
    ax5.set_xlabel('X [m]')
    ax5.set_ylabel('Y [m]')
    ax5.axis('equal')
    plt.colorbar(contour_tke, ax=ax5)
    
    # 6. Flow statistics
    ax6 = axes[1, 2]
    
    # Remove ax6 and create text summary
    fig.delaxes(ax6)
    
    # Calculate flow statistics
    max_speed = np.max(speed)
    avg_speed = np.mean(speed)
    coral_area = np.pi * 0.02**2
    domain_area = 0.224**2
    blockage_ratio = coral_area / domain_area * 100
    
    # Reynolds number estimation
    U_inlet = 0.1  # m/s
    L_char = 0.04  # coral diameter
    nu = 1e-6  # kinematic viscosity
    Re = U_inlet * L_char / nu
    
    stats_text = f"""
Flow Statistics:

Max Velocity: {max_speed:.3f} m/s
Avg Velocity: {avg_speed:.3f} m/s
Reynolds Number: {Re:.0f}
Blockage Ratio: {blockage_ratio:.1f}%

Coral Dimensions:
Diameter: 40 mm
Height: 50 mm
Area: {coral_area*1e6:.1f} mm²

Domain Size: 224×224×160 mm
Simulation: Two-phase flow
Turbulence: k-ω SST model
    """
    
    fig.text(0.68, 0.25, stats_text, fontsize=10, verticalalignment='top',
             bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgray", alpha=0.8))
    
    plt.tight_layout()
    
    # Save detailed analysis
    output_file = 'detailed_flow_analysis_BR01.png'
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"Detailed flow analysis saved: {output_file}")
    
    return fig

def main():
    """Main function to generate current contour plots"""
    
    print("🌊 Current Contour Plot Generator for BR01 Coral Simulation")
    print("=" * 60)
    
    case_dir = os.getcwd()
    print(f"Working directory: {case_dir}")
    
    # Check if we're in a case directory
    if not os.path.exists('0') or not os.path.exists('constant'):
        print("❌ Not in an OpenFOAM case directory!")
        print("Please run this script from the case directory (e.g., cases/BR01_wave_case/)")
        return
    
    # Create synthetic data for demonstration
    print("📊 Generating synthetic current field data...")
    X, Y, U, V, speed = create_synthetic_current_data()
    
    # Create basic contour plots
    print("🎨 Creating current contour plots...")
    fig1 = plot_current_contours(X, Y, U, V, speed)
    
    # Create detailed analysis
    print("🔬 Creating detailed flow analysis...")
    fig2 = create_detailed_flow_analysis(X, Y, U, V, speed)
    
    # Show plots
    # plt.show()  # Disabled for headless operation
    
    print("\n✅ Current contour plots generated successfully!")
    print("📁 Output files:")
    print("   - current_contours_BR01_t0.1.png")
    print("   - detailed_flow_analysis_BR01.png")
    
    print("\n💡 To use with real simulation data:")
    print("   1. Run the OpenFOAM simulation to completion")
    print("   2. Use 'sample' utility to extract field data")
    print("   3. Modify this script to read actual data files")

if __name__ == "__main__":
    main()