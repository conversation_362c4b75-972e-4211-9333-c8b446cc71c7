#!/usr/bin/env python3
"""
Simple Current Contour Plotter for OpenFOAM BR01 Case
Uses ParaView/VTK utilities to extract and visualize current data
"""

import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
import numpy as np
import os
import subprocess
import json

def create_paraview_script():
    """Create ParaView python script for data extraction"""
    script_content = '''
# ParaView script to extract current data
import paraview.simple as pvs

# Open the case
case = pvs.OpenFOAMReader(FileName='BR01_wave_case.foam')
case.MeshRegions = ['internalMesh']
case.CellArrays = ['U', 'alpha.water', 'p_rgh']

# Get available time steps
time_steps = case.TimestepValues
if len(time_steps) > 1:
    # Use the last time step
    pvs.GetAnimationScene().AnimationTime = time_steps[-1]
    print(f"Using time step: {time_steps[-1]}")
else:
    print("Using initial time step: 0")

# Create a slice through the middle of the domain (z=0.08)
slice1 = pvs.Slice(Input=case)
slice1.SliceType = 'Plane'
slice1.SliceType.Origin = [0.0, 0.0, 0.08]
slice1.SliceType.Normal = [0.0, 0.0, 1.0]

# Calculate velocity magnitude
calculator = pvs.Calculator(Input=slice1)
calculator.AttributeType = 'Cell Data'
calculator.ResultArrayName = 'VMag'
calculator.Function = 'mag(U)'

# Save as CSV
writer = pvs.CreateWriter('current_data.csv', calculator)
writer.WriteAllTimeSteps = 0
writer.UpdatePipeline()

print("Data extraction completed")
'''
    
    with open('extract_current_data.py', 'w') as f:
        f.write(script_content)
    
    return 'extract_current_data.py'

def run_simulation_check():
    """Check simulation status and available time steps"""
    
    print("🔍 Checking simulation status...")
    
    # List time directories
    time_dirs = []
    for item in os.listdir('.'):
        try:
            # Check if it's a valid time directory
            float(item)
            if os.path.isdir(item):
                time_dirs.append(float(item))
        except ValueError:
            continue
    
    time_dirs.sort()
    
    if len(time_dirs) == 0:
        print("❌ No simulation time steps found!")
        return None
    
    print(f"✅ Found {len(time_dirs)} time steps: {time_dirs}")
    
    # Check latest time step for fields
    latest_time = str(time_dirs[-1]) if time_dirs[-1] != 0.0 else '0'
    latest_dir = latest_time
    
    if os.path.exists(latest_dir):
        fields = [f for f in os.listdir(latest_dir) if os.path.isfile(os.path.join(latest_dir, f))]
        print(f"📊 Available fields in t={latest_time}: {fields}")
        
        return {
            'time_dirs': time_dirs,
            'latest_time': latest_time,
            'fields': fields
        }
    
    return None

def create_simple_mesh_visualization():
    """Create a simple mesh and flow visualization"""
    
    print("🎨 Creating mesh and flow visualization...")
    
    # Read coral geometry info
    try:
        with open('DOMAIN_VERIFICATION.md', 'r') as f:
            content = f.read()
            # Extract coral dimensions if available
            # This is just for visualization purposes
    except:
        pass
    
    # Create figure
    fig, axes = plt.subplots(2, 2, figsize=(14, 10))
    fig.suptitle('BR01 Coral Current Analysis - Domain Overview', fontsize=16, fontweight='bold')
    
    # 1. Domain overview
    ax1 = axes[0, 0]
    
    # Draw domain boundaries
    domain_x = [-0.112, 0.112, 0.112, -0.112, -0.112]
    domain_y = [-0.112, -0.112, 0.112, 0.112, -0.112]
    ax1.plot(domain_x, domain_y, 'k-', linewidth=2, label='Domain')
    
    # Draw coral (approximate position and size based on scaling)
    coral_circle = plt.Circle((0, 0), 0.011, color='brown', alpha=0.7, label='BR01 Coral')
    ax1.add_patch(coral_circle)
    
    # Add inlet/outlet indicators
    ax1.arrow(-0.1, 0, 0.03, 0, head_width=0.01, head_length=0.005, fc='blue', ec='blue')
    ax1.text(-0.105, -0.02, 'Inlet', ha='center', fontsize=10, color='blue')
    ax1.arrow(0.07, 0, 0.03, 0, head_width=0.01, head_length=0.005, fc='red', ec='red')
    ax1.text(0.095, -0.02, 'Outlet', ha='center', fontsize=10, color='red')
    
    ax1.set_xlim(-0.12, 0.12)
    ax1.set_ylim(-0.12, 0.12)
    ax1.set_xlabel('X [m]')
    ax1.set_ylabel('Y [m]')
    ax1.set_title('Domain Layout')
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    ax1.axis('equal')
    
    # 2. Expected flow pattern (qualitative)
    ax2 = axes[0, 1]
    
    # Create synthetic flow field around coral
    x = np.linspace(-0.05, 0.05, 20)
    y = np.linspace(-0.05, 0.05, 20)
    X, Y = np.meshgrid(x, y)
    
    # Simple potential flow around cylinder
    coral_radius = 0.011
    R = np.sqrt(X**2 + Y**2)
    R = np.maximum(R, coral_radius * 1.1)  # Avoid singularity
    
    U_base = 0.1
    U = U_base * (1 - (coral_radius**2 / R**2))
    V = np.zeros_like(U)
    
    # Mask inside coral
    inside = R < coral_radius * 1.2
    U[inside] = 0
    V[inside] = 0
    
    ax2.streamplot(X, Y, U, V, density=1.5, color='blue')
    coral_circle2 = plt.Circle((0, 0), coral_radius, color='brown', alpha=0.8)
    ax2.add_patch(coral_circle2)
    
    ax2.set_xlim(-0.05, 0.05)
    ax2.set_ylim(-0.05, 0.05)
    ax2.set_xlabel('X [m]')
    ax2.set_ylabel('Y [m]')
    ax2.set_title('Expected Flow Pattern')
    ax2.grid(True, alpha=0.3)
    ax2.axis('equal')
    
    # 3. Coral geometry details
    ax3 = axes[1, 0]
    ax3.text(0.05, 0.9, 'BR01 Branching Coral Specifications:', fontsize=12, fontweight='bold', transform=ax3.transAxes)
    
    specs_text = """
• Morphology: Branching
• Scaled Height: 5.0 cm
• Original Height: 30 cm (scaled by 1/6)
• Diameter: ~2.2 cm
• Volume: Complex 3D structure
• Surface Area: High (branching increases area)
• Flow Resistance: High drag coefficient
• Reynolds Number: ~4,000 (turbulent flow)

Expected Wave Energy Reduction:
• Branching corals: 40-60% energy dissipation
• High surface roughness
• Complex wake formation
• Significant turbulence generation
    """
    
    ax3.text(0.05, 0.8, specs_text, fontsize=10, transform=ax3.transAxes, verticalalignment='top')
    ax3.set_xlim(0, 1)
    ax3.set_ylim(0, 1)
    ax3.axis('off')
    
    # 4. Mesh statistics
    ax4 = axes[1, 1]
    ax4.text(0.05, 0.9, 'Mesh and Simulation Info:', fontsize=12, fontweight='bold', transform=ax4.transAxes)
    
    mesh_text = """
Final Mesh Statistics:
• Cells: 111,167 (refined from 72,000)
• Faces: 358,785
• Points: 137,120
• Refinement levels: 0-2
• Coral surface: Wall boundary condition

Simulation Parameters:
• Solver: interFoam (two-phase VOF)
• Turbulence: k-ω SST model
• Time step: 0.01 s
• Total time: 10 s (10 wave periods)
• Seawater density: 1025 kg/m³
• Dynamic viscosity: 0.00097 Pa·s

Boundary Conditions:
• Inlet: Oscillating flow (wave-like)
• Outlet: Zero gradient
• Coral: No-slip wall
• Free surface: Atmosphere patch
    """
    
    ax4.text(0.05, 0.8, mesh_text, fontsize=9, transform=ax4.transAxes, verticalalignment='top')
    ax4.set_xlim(0, 1)
    ax4.set_ylim(0, 1)
    ax4.axis('off')
    
    plt.tight_layout()
    
    # Save plot
    output_file = 'BR01_current_analysis_overview.png'
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"✅ Overview plot saved: {output_file}")
    
    return fig

def extract_openfoam_data():
    """Try to extract data using OpenFOAM utilities"""
    
    print("🔄 Attempting to extract current data from OpenFOAM...")
    
    try:
        # Create a sample dictionary for field extraction
        sample_dict = """
/*--------------------------------*- C++ -*----------------------------------*\\
FoamFile
{
    version     2.0;
    format      ascii;
    class       dictionary;
    location    "system";
    object      sampleDict;
}

type surfaces;
libs ("libsampling.so");

writeControl timeStep;
writeInterval 1;

interpolationScheme cellPoint;
surfaceFormat raw;

surfaces
(
    midPlane
    {
        type plane;
        basePoint (0 0 0.08);
        normalVector (0 0 1);
        interpolate true;
    }
);

fields
(
    U
    alpha.water
    p_rgh
);
"""
        
        # Write sample dictionary
        os.makedirs('system', exist_ok=True)
        with open('system/sampleDict', 'w') as f:
            f.write(sample_dict)
        
        # Try to run sample utility
        result = subprocess.run([
            'docker', 'run', '--rm', '-v', f'{os.getcwd()}:/home/<USER>',
            '--user', f'{os.getuid()}:{os.getgid()}',
            'opencfd/openfoam-run:latest',
            'sample', '-latestTime'
        ], capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ Data extraction successful!")
            print(result.stdout[-300:])  # Show last part of output
            
            # Check for postProcessing directory
            if os.path.exists('postProcessing'):
                print("📁 PostProcessing data available")
                return True
        else:
            print("❌ Data extraction failed:")
            print(result.stderr[-300:])
            
    except Exception as e:
        print(f"❌ Error during data extraction: {e}")
    
    return False

def main():
    """Main function"""
    
    print("🌊 BR01 Coral Current Contour Plot Generator")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not os.path.exists('constant') or not os.path.exists('0'):
        print("❌ Please run this script from the BR01_wave_case directory!")
        return
    
    # Check simulation status
    sim_info = run_simulation_check()
    
    # Create visualization
    fig = create_simple_mesh_visualization()
    
    # Try to extract real data if simulation has run
    if sim_info and len(sim_info['time_dirs']) > 1:
        print(f"\n📊 Simulation data available for {len(sim_info['time_dirs'])} time steps")
        extracted = extract_openfoam_data()
        
        if extracted:
            print("🎯 Real data extraction successful - you can now create detailed contours!")
        else:
            print("⚠️  Using overview visualization only")
    else:
        print("\n⚠️  No simulation results found - showing domain overview only")
        print("💡 Run the OpenFOAM simulation first to get current data")
    
    # Show the plot
    # plt.show()  # Disabled for headless operation
    
    print("\n✅ Current analysis visualization complete!")
    print(f"📁 Visualization saved as: {output_file}")
    print("\n📝 Next steps to get detailed current contours:")
    print("   1. Run the full OpenFOAM simulation: interFoam")
    print("   2. Use ParaView to visualize results: paraview BR01_wave_case.foam")
    print("   3. Or use this script after simulation completion")

if __name__ == "__main__":
    main()