
# ParaView Python script for longitudinal cross-sectional plots
import paraview.simple as pv

# Load the case
case_file = "BR01_wave_case/BR01_wave_case.foam"
reader = pv.OpenFOAMReader(FileName=case_file)

# Get the latest time step
reader.UpdatePipeline()
times = reader.TimestepValues
if times:
    latest_time = times[-1]
    reader.SMProxy.SetPropertyWithName("TimestepValues", [latest_time])

# Create a longitudinal slice (Y-Z plane through the center)
slice1 = pv.Slice(Input=reader)
slice1.SliceType = 'Plane'
slice1.SliceType.Origin = [0.2, 0.0, 0.0]  # Middle of domain
slice1.SliceType.Normal = [1.0, 0.0, 0.0]  # X-normal plane

# Set up the render view
renderView = pv.GetActiveViewOrCreate('RenderView')
renderView.ViewSize = [1200, 800]
renderView.CameraPosition = [0.5, 0.0, 0.3]
renderView.CameraFocalPoint = [0.2, 0.0, 0.0]

# Display the slice
sliceDisplay = pv.Show(slice1, renderView)

# Plot 1: Alpha.water (wave field)
pv.ColorBy(sliceDisplay, ('POINTS', 'alpha.water'))
sliceDisplay.RescaleTransferFunctionToDataRange(True, False)
sliceDisplay.SetScalarBarVisibility(renderView, True)

# Get color transfer function
alphaTF = pv.GetColorTransferFunction('alpha.water')
alphaTF.RescaleTransferFunction(0.0, 1.0)

# Set view and save
renderView.CameraViewUp = [0.0, 0.0, 1.0]
pv.WriteImage('BR01_wave_case/alpha_water_longitudinal.png')

# Plot 2: Velocity magnitude
U_data = reader.PointData.GetArray('U')
if U_data:
    calc = pv.Calculator(Input=slice1)
    calc.ResultArrayName = 'U_magnitude'
    calc.Function = 'mag(U)'
    
    calcDisplay = pv.Show(calc, renderView)
    pv.Hide(slice1, renderView)
    
    pv.ColorBy(calcDisplay, ('POINTS', 'U_magnitude'))
    calcDisplay.RescaleTransferFunctionToDataRange(True, False)
    
    # Get color transfer function for velocity
    velTF = pv.GetColorTransferFunction('U_magnitude')
    velTF.ApplyPreset('Rainbow Desaturated', True)
    
    pv.WriteImage('BR01_wave_case/velocity_magnitude_longitudinal.png')
    pv.Hide(calc, renderView)

# Plot 3: Pressure field
if reader.PointData.GetArray('p_rgh'):
    pv.Show(slice1, renderView)
    pv.ColorBy(sliceDisplay, ('POINTS', 'p_rgh'))
    sliceDisplay.RescaleTransferFunctionToDataRange(True, False)
    
    pressTF = pv.GetColorTransferFunction('p_rgh')
    pressTF.ApplyPreset('Cool to Warm', True)
    
    pv.WriteImage('BR01_wave_case/pressure_longitudinal.png')

# Plot 4: Turbulent kinetic energy
if reader.PointData.GetArray('k'):
    pv.ColorBy(sliceDisplay, ('POINTS', 'k'))
    sliceDisplay.RescaleTransferFunctionToDataRange(True, False)
    
    kTF = pv.GetColorTransferFunction('k')
    kTF.ApplyPreset('Viridis (matplotlib)', True)
    
    pv.WriteImage('BR01_wave_case/turbulent_ke_longitudinal.png')

print("Contour plots generated successfully:")
print("- alpha_water_longitudinal.png (wave field)")
print("- velocity_magnitude_longitudinal.png (flow field)")  
print("- pressure_longitudinal.png (pressure distribution)")
print("- turbulent_ke_longitudinal.png (turbulence intensity)")
