FoamFile
{
    version         2;
    format          ascii;
    class           dictionary;
    object          surfaceFeaturesDict;
}

// Exported by SnappyHexMesh GUI add-on for Blender v1.8
// Source file: C:\Users\<USER>\openFOAM - Coral Simulation\BranchingCoralMesh.blend
// Export date: 2025-08-01 10:02:34.772528



surfaces
(
    "Coral.stl"
    "Ground.stl"
    "Inlet.stl"
    "Outlet.stl"
    "Wall.stl"
);

includedAngle 150;
writeObj yes;


