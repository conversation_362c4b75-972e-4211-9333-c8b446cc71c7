#!/usr/bin/env python3
"""
Scale and reposition BR01 branching coral STL to fit the wave domain
"""

import struct
import numpy as np

def read_stl_binary(filename):
    """Read binary STL file"""
    with open(filename, 'rb') as f:
        header = f.read(80)
        num_triangles = struct.unpack('<I', f.read(4))[0]
        
        triangles = []
        
        for i in range(num_triangles):
            # Read normal vector
            normal = struct.unpack('<3f', f.read(12))
            
            # Read 3 vertices
            v1 = struct.unpack('<3f', f.read(12))
            v2 = struct.unpack('<3f', f.read(12))
            v3 = struct.unpack('<3f', f.read(12))
            
            # Skip attribute bytes
            f.read(2)
            
            triangles.append([normal, v1, v2, v3])
        
        return header, triangles, num_triangles

def write_stl_binary(filename, header, triangles):
    """Write binary STL file"""
    with open(filename, 'wb') as f:
        # Write header (80 bytes)
        f.write(header)
        
        # Write number of triangles
        f.write(struct.pack('<I', len(triangles)))
        
        # Write triangles
        for triangle in triangles:
            normal, v1, v2, v3 = triangle
            
            # Write normal
            f.write(struct.pack('<3f', *normal))
            
            # Write vertices
            f.write(struct.pack('<3f', *v1))
            f.write(struct.pack('<3f', *v2))
            f.write(struct.pack('<3f', *v3))
            
            # Write attribute bytes (0)
            f.write(struct.pack('<H', 0))

def transform_coral(triangles):
    """Scale and reposition coral to fit domain"""
    
    # Current coral bounds: X: -0.2 to 1.1, Z: 0.004 to 0.304
    # Target: 5cm coral centered in 22.4cm domain
    
    # Scale factor: 30cm -> 5cm = 1/6
    scale_factor = 1.0 / 6.0
    
    # Target position: center the coral in the domain
    # Domain center: (0, 0, 0.08) - middle of 16cm height
    # Place coral base at Z=0.01, so coral top is at Z=0.06 (5cm height)
    
    target_center_x = 0.0      # Center of domain
    target_center_y = 0.0      # Center of domain  
    target_base_z = 0.01       # 1cm above bottom
    
    # Original coral center and base
    orig_center_x = 0.45       # From analysis
    orig_center_y = 0.0
    orig_base_z = 0.004003
    
    transformed_triangles = []
    
    for triangle in triangles:
        normal, v1, v2, v3 = triangle
        
        # Transform each vertex
        new_vertices = []
        for vertex in [v1, v2, v3]:
            x, y, z = vertex
            
            # Scale
            x_scaled = x * scale_factor
            y_scaled = y * scale_factor  
            z_scaled = z * scale_factor
            
            # Translate to target position
            x_final = x_scaled - (orig_center_x * scale_factor) + target_center_x
            y_final = y_scaled - (orig_center_y * scale_factor) + target_center_y
            z_final = z_scaled - (orig_base_z * scale_factor) + target_base_z
            
            new_vertices.append((x_final, y_final, z_final))
        
        # Keep normal unchanged (should be unit vector)
        transformed_triangles.append([normal, new_vertices[0], new_vertices[1], new_vertices[2]])
    
    return transformed_triangles

def main():
    input_file = "geom/BR01_branching.stl"
    output_file = "constant/triSurface/coral.stl"
    
    print("=== Scaling and Repositioning BR01 Branching Coral ===")
    print(f"Input: {input_file}")
    print(f"Output: {output_file}")
    
    # Read original STL
    header, triangles, num_triangles = read_stl_binary(input_file)
    print(f"Original triangles: {num_triangles}")
    
    # Transform coral
    transformed_triangles = transform_coral(triangles)
    print(f"Transformed triangles: {len(transformed_triangles)}")
    
    # Write new STL
    write_stl_binary(output_file, header, transformed_triangles)
    
    print("Transformation completed!")
    print("")
    print("Scale factor: 1/6 (30cm -> 5cm)")
    print("Position: Centered in domain at Z=0.01-0.06m")
    print("Domain: 22.4cm x 22.4cm x 16cm")
    print("Coral: ~3.7cm x 3.3cm x 5cm (scaled)")

if __name__ == "__main__":
    main()