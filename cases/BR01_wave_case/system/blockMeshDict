/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2.1.0                                 |
|   \\  /    A nd           | Web:      www.OpenFOAM.org                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       dictionary;
    object      blockMeshDict;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
// OPTIMIZED FOR LOW-SPEED COMPUTERS
// Mesh resolution options (uncomment one):

scale 1;

vertices
(
    (-0.112 -0.112 0)        // 0
    (0.112 -0.112 0)        // 1  
    (0.112 0.112 0)        // 2
    (-0.112 0.112 0)        // 3
    (-0.112 -0.112 0.160)  // 4
    (0.112 -0.112 0.160)  // 5
    (0.112 0.112 0.160)  // 6
    (-0.112 0.112 0.160)  // 7
);

blocks
(
    // PERFORMANCE LEVELS (choose one):
    
    // ULTRA-LOW: 20x20x8 = 3,200 cells (fastest, basic accuracy)
    // hex (0 1 2 3 4 5 6 7) (30 30 10) simpleGrading (1 1 1)
    
    // LOW: 25x25x8 = 5,000 cells (fast, reasonable accuracy)
    // hex (0 1 2 3 4 5 6 7) (30 30 10) simpleGrading (1 1 1)
    
    // MEDIUM: 30x30x10 = 9,000 cells (balanced performance/accuracy)
    hex (0 1 2 3 4 5 6 7) (30 30 10) simpleGrading (1 1 1)
    
    // HIGH: 40x40x12 = 19,200 cells (slower but better accuracy)
    // hex (0 1 2 3 4 5 6 7) (30 30 10) simpleGrading (1 1 1)
    
    // ORIGINAL: 60x60x20 = 72,000 cells (very slow on low-end hardware)
    // hex (0 1 2 3 4 5 6 7) (30 30 10) simpleGrading (1 1 1)
);

edges
(
);

boundary
(
    inlet
    {
        type patch;
        faces
        (
            (0 4 7 3)
        );
    }
    outlet
    {
        type patch;
        faces
        (
            (2 6 5 1)
        );
    }
    front
    {
        type wall;
        faces
        (
            (1 5 4 0)
        );
    }
    back
    {
        type wall;
        faces
        (
            (3 7 6 2)
        );
    }
    bottom
    {
        type wall;
        faces
        (
            (0 3 2 1)
        );
    }
    atmosphere
    {
        type patch;
        faces
        (
            (4 5 6 7)
        );
    }
);

mergePatchPairs
(
);

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
