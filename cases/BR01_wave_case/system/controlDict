/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2.1.0                                 |
|   \\  /    A nd           | Web:      www.OpenFOAM.org                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       dictionary;
    location    "system";
    object      controlDict;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

application     interFoam;

startFrom       startTime;

startTime       0;

stopAt          endTime;

endTime         10;             // 10 seconds (10 wave periods)

deltaT          0.0001;         // Very small initial time step

writeControl    adjustableRunTime;

writeInterval   0.1;             // Write every 0.1s

purgeWrite      0;

writeFormat     binary;

writePrecision  8;

writeCompression off;

timeFormat      general;

timePrecision   6;

runTimeModifiable true;

// Advanced automatic time stepping
adjustTimeStep  yes;

maxCo           0.25;           // Conservative Courant number
maxAlphaCo      0.25;           // Conservative interface Courant number  
maxCapillaryco  0.25;           // Surface tension Courant number

minDeltaT       1e-8;           // Minimum time step (safety)
maxDeltaT       0.005;          // Maximum time step (wave resolution)

// Automatic time step adjustment based on residuals
maxDeltaTIncreaseRatio  1.2;    // Max increase per step (20%)
maxDeltaTDecreaseRatio  0.5;    // Max decrease per step (50%)

// Target residual control for time stepping
targetResidualControl
{
    p_rgh           1e-3;       // Pressure residual target
    U               1e-4;       // Velocity residual target
    alpha.water     1e-5;       // Phase residual target
}

functions
{
    forces
    {
        type            forces;
        libs            ("libforces.so");
        writeControl    timeStep;
        writeInterval   1;
        patches         (coral);
        rho             rhoInf;
        rhoInf          1023;            // Seawater density
        CofR            (0 0 0);
        log             true;
    }

    forceCoeffs
    {
        type            forceCoeffs;
        libs            ("libforces.so");
        writeControl    timeStep;
        writeInterval   1;
        patches         (coral);
        rho             rhoInf;
        rhoInf          1023;            // Seawater density
        liftDir         (0 0 1);
        dragDir         (1 0 0);
        CofR            (0 0 0);
        pitchAxis       (0 1 0);
        magUInf         0.16;            // Reference velocity
        lRef            0.05;            // Reference length (scaled coral)
        Aref            0.0025;          // Reference area (5cm x 5cm)
        log             true;
    }
}

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
