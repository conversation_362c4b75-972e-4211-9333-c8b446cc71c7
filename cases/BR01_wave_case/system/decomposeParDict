FoamFile
{
    version     2.0;
    format      ascii;
    class       dictionary;
    object      decomposeParDict;
}

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

numberOfSubdomains 4;

method          scotch;

coeffs
{
}

// Alternative methods for decomposition:

simpleCoeffs
{
    n               (2 2 1);
    delta           0.001;
}

hierarchicalCoeffs
{
    n               (2 2 1);
    delta           0.001;
    order           xyz;
}

metisCoeffs
{
    processorWeights
    (
        1
        1
        1
        1
    );
}

scotchCoeffs
{
    processorWeights
    (
        1
        1
        1
        1
    );
    // writeGraph      true;
    // strategy        "b";
}

manualCoeffs
{
    dataFile        "cellDecomposition";
}

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //