/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2.1.0                                 |
|   \\  /    A nd           | Web:      www.OpenFOAM.org                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       dictionary;
    location    "system";
    object      fvSolution;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

solvers
{
    "alpha.water.*"
    {
        nAlphaCorr      3;
        nAlphaSubCycles 2;
        cAlpha          1;

        MULESCorr       yes;
        nLimiterIter    5;
        
        solver          smoothSolver;
        smoother        symGaussSeidel;
        tolerance       1e-10;
        relTol          0;
        
        implicitPhasePressure yes;
        
        nAlphaBounds    3;
        snapTol         1e-12;
        clip            true;
    }

    "pcorr.*"
    {
        solver          PCG;
        preconditioner  DIC;
        tolerance       1e-5;
        relTol          0;
    }

    p_rgh
    {
        solver          GAMG;
        tolerance       1e-07;
        relTol          0.05;
        smoother        DICGaussSeidel;
    }

    p_rghFinal
    {
        $p_rgh;
        relTol          0;
    }

    U
    {
        solver          smoothSolver;
        smoother        symGaussSeidel;
        tolerance       1e-06;
        relTol          0.1;
    }

    UFinal
    {
        $U;
        relTol          0;
    }

    "(k|omega).*"
    {
        solver          smoothSolver;
        smoother        symGaussSeidel;
        tolerance       1e-08;
        relTol          0.1;
    }
}

PIMPLE
{
    momentumPredictor   yes;
    nOuterCorrectors    3;
    nCorrectors         4;
    nNonOrthogonalCorrectors 2;
    
    pRefCell        0;
    pRefValue       0;
    
    // Outer corrector residual control for automatic time stepping
    outerCorrectorResidualControl
    {
        p_rgh
        {
            tolerance   1e-3;
            relTol      0;
        }
        U
        {
            tolerance   1e-4;
            relTol      0;
        }
    }
    
    // Standard residual control  
    residualControl
    {
        p_rgh
        {
            tolerance   1e-5;
            relTol      0;
        }
        U
        {
            tolerance   1e-6;
            relTol      0;
        }
        "alpha.*"
        {
            tolerance   1e-7;
            relTol      0;
        }
        "(k|omega).*"
        {
            tolerance   1e-5;
            relTol      0;
        }
    }
    
    // Adaptive relaxation factors
    relaxationFactors
    {
        equations
        {
            "U.*"           0.7;
            "p_rgh.*"       0.3;
            "alpha.*"       0.9;
            "(k|omega).*"   0.7;
        }
    }
    
    // Turbulence update frequency
    turbOnFinalIterOnly false;
}

SIMPLE
{
    pRefCell        0;
    pRefValue       0;
}

relaxationFactors
{
    fields
    {
        p_rgh           0.3;
    }
    equations
    {
        U               0.7;
        "(k|omega).*"   0.7;
    }
}

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //