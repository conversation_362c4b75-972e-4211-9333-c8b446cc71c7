/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2212                                   |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       dictionary;
    object      sampleDict;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

type            sets;
libs            ("libsampling.so");

interpolationScheme cellPoint;

setFormat       raw;

sets
(
    longitudinalSlice
    {
        type    uniform;
        axis    z;
        start   (0.0 -0.1 0.005);
        end     (0.0 -0.1 0.155);
        nPoints 50;
    }
    
    centerlineSlice
    {
        type    uniform;
        axis    z;
        start   (0.0 0.0 0.005);
        end     (0.0 0.0 0.155);
        nPoints 50;
    }
    
    // Horizontal slice through middle height
    horizontalSlice
    {
        type    uniform;
        axis    x;
        start   (-0.1 0.0 0.08);
        end     (0.1 0.0 0.08);
        nPoints 50;
    }
);

surfaces
(
    longitudinalPlane
    {
        type            plane;
        planeType       pointAndNormal;
        pointAndNormalDict
        {
            point   (0.0 0.0 0.08);
            normal  (0 1 0);
        }
        interpolate     true;
    }
);

fields          (alpha.water U p_rgh k omega);

// ************************************************************************* //