#!/usr/bin/env python3
"""
Detailed numerical verification of coral positioning in domain
"""

import struct
import numpy as np

def read_stl_binary(filename):
    """Read binary STL file"""
    with open(filename, 'rb') as f:
        header = f.read(80)
        num_triangles = struct.unpack('<I', f.read(4))[0]
        
        vertices = []
        
        for i in range(num_triangles):
            f.read(12)  # Skip normal
            v1 = struct.unpack('<3f', f.read(12))
            v2 = struct.unpack('<3f', f.read(12))
            v3 = struct.unpack('<3f', f.read(12))
            vertices.extend([v1, v2, v3])
            f.read(2)   # Skip attributes
        
        return np.array(vertices), num_triangles

def analyze_cross_sections():
    """Detailed cross-sectional analysis"""
    
    print("=" * 80)
    print("🔍 DETAILED CROSS-SECTIONAL DOMAIN VERIFICATION")
    print("=" * 80)
    
    vertices, num_triangles = read_stl_binary("constant/triSurface/coral.stl")
    
    # Domain boundaries from blockMeshDict
    domain_bounds = {
        'x_min': -0.112, 'x_max': 0.112,  # 22.4cm total
        'y_min': -0.112, 'y_max': 0.112,  # 22.4cm total  
        'z_min': 0.000,  'z_max': 0.160   # 16.0cm total
    }
    
    domain_size = {
        'x': domain_bounds['x_max'] - domain_bounds['x_min'],
        'y': domain_bounds['y_max'] - domain_bounds['y_min'],
        'z': domain_bounds['z_max'] - domain_bounds['z_min']
    }
    
    # Coral bounds
    coral_bounds = {
        'x_min': np.min(vertices[:, 0]), 'x_max': np.max(vertices[:, 0]),
        'y_min': np.min(vertices[:, 1]), 'y_max': np.max(vertices[:, 1]),
        'z_min': np.min(vertices[:, 2]), 'z_max': np.max(vertices[:, 2])
    }
    
    coral_size = {
        'x': coral_bounds['x_max'] - coral_bounds['x_min'],
        'y': coral_bounds['y_max'] - coral_bounds['y_min'],
        'z': coral_bounds['z_max'] - coral_bounds['z_min']
    }
    
    coral_center = {
        'x': (coral_bounds['x_min'] + coral_bounds['x_max']) / 2,
        'y': (coral_bounds['y_min'] + coral_bounds['y_max']) / 2,
        'z': (coral_bounds['z_min'] + coral_bounds['z_max']) / 2
    }
    
    print(f"\n📐 DOMAIN SPECIFICATIONS:")
    print(f"   X-direction: {domain_bounds['x_min']:8.4f} to {domain_bounds['x_max']:8.4f} m ({domain_size['x']*100:5.1f} cm)")
    print(f"   Y-direction: {domain_bounds['y_min']:8.4f} to {domain_bounds['y_max']:8.4f} m ({domain_size['y']*100:5.1f} cm)")
    print(f"   Z-direction: {domain_bounds['z_min']:8.4f} to {domain_bounds['z_max']:8.4f} m ({domain_size['z']*100:5.1f} cm)")
    
    print(f"\n🪸 CORAL ACTUAL POSITION:")
    print(f"   X-bounds:    {coral_bounds['x_min']:8.4f} to {coral_bounds['x_max']:8.4f} m ({coral_size['x']*100:5.1f} cm)")
    print(f"   Y-bounds:    {coral_bounds['y_min']:8.4f} to {coral_bounds['y_max']:8.4f} m ({coral_size['y']*100:5.1f} cm)")
    print(f"   Z-bounds:    {coral_bounds['z_min']:8.4f} to {coral_bounds['z_max']:8.4f} m ({coral_size['z']*100:5.1f} cm)")
    
    print(f"\n🎯 CORAL CENTER POSITION:")
    print(f"   X-center:    {coral_center['x']:8.5f} m ({coral_center['x']*1000:+6.2f} mm from domain center)")
    print(f"   Y-center:    {coral_center['y']:8.5f} m ({coral_center['y']*1000:+6.2f} mm from domain center)")
    print(f"   Z-center:    {coral_center['z']:8.5f} m ({coral_center['z']*100:5.2f} cm above sea floor)")
    
    # Cross-sectional analysis
    print(f"\n✂️ CROSS-SECTIONAL CUTS:")
    
    # 1. YZ cross-section at X=0 (center)
    tolerance = 0.005  # 5mm tolerance
    center_cut = vertices[np.abs(vertices[:, 0]) < tolerance]
    print(f"   YZ Cut (X=0±{tolerance*1000:.0f}mm): {len(center_cut):,} points")
    
    if len(center_cut) > 0:
        y_range = np.max(center_cut[:, 1]) - np.min(center_cut[:, 1])
        z_range = np.max(center_cut[:, 2]) - np.min(center_cut[:, 2])
        print(f"      Y-span: {y_range*100:5.1f} cm, Z-span: {z_range*100:5.1f} cm")
    
    # 2. XZ cross-section at Y=0 (wave path)
    wave_cut = vertices[np.abs(vertices[:, 1]) < tolerance]
    print(f"   XZ Cut (Y=0±{tolerance*1000:.0f}mm): {len(wave_cut):,} points")
    
    if len(wave_cut) > 0:
        x_range = np.max(wave_cut[:, 0]) - np.min(wave_cut[:, 0])
        z_range = np.max(wave_cut[:, 2]) - np.min(wave_cut[:, 2])
        print(f"      X-span: {x_range*100:5.1f} cm, Z-span: {z_range*100:5.1f} cm")
    
    # 3. XY cross-section at coral mid-height
    mid_height = coral_center['z']
    top_cut = vertices[np.abs(vertices[:, 2] - mid_height) < tolerance]
    print(f"   XY Cut (Z={mid_height:.3f}±{tolerance*1000:.0f}mm): {len(top_cut):,} points")
    
    if len(top_cut) > 0:
        x_range = np.max(top_cut[:, 0]) - np.min(top_cut[:, 0])
        y_range = np.max(top_cut[:, 1]) - np.min(top_cut[:, 1])
        print(f"      X-span: {x_range*100:5.1f} cm, Y-span: {y_range*100:5.1f} cm")
    
    # Clearance analysis
    print(f"\n🚧 CLEARANCE VERIFICATION:")
    
    clearances = {
        'x_left':  coral_bounds['x_min'] - domain_bounds['x_min'],
        'x_right': domain_bounds['x_max'] - coral_bounds['x_max'],
        'y_front': coral_bounds['y_min'] - domain_bounds['y_min'],
        'y_back':  domain_bounds['y_max'] - coral_bounds['y_max'],
        'z_bottom': coral_bounds['z_min'] - domain_bounds['z_min'],
        'z_top':   domain_bounds['z_max'] - coral_bounds['z_max']
    }
    
    print(f"   Left (X-):   {clearances['x_left']*1000:6.1f} mm")
    print(f"   Right (X+):  {clearances['x_right']*1000:6.1f} mm")
    print(f"   Front (Y-):  {clearances['y_front']*1000:6.1f} mm")
    print(f"   Back (Y+):   {clearances['y_back']*1000:6.1f} mm")
    print(f"   Bottom (Z-): {clearances['z_bottom']*1000:6.1f} mm ({clearances['z_bottom']*100:4.1f} cm)")
    print(f"   Top (Z+):    {clearances['z_top']*1000:6.1f} mm ({clearances['z_top']*100:4.1f} cm)")
    
    # Wave interaction analysis
    print(f"\n🌊 WAVE INTERACTION GEOMETRY:")
    
    wave_entry = domain_bounds['x_min']
    coral_start = coral_bounds['x_min']
    coral_end = coral_bounds['x_max']
    wave_exit = domain_bounds['x_max']
    
    upstream_length = coral_start - wave_entry
    coral_length = coral_end - coral_start
    downstream_length = wave_exit - coral_end
    
    print(f"   Wave entry:       X = {wave_entry*100:6.1f} cm")
    print(f"   Coral starts:     X = {coral_start*100:6.1f} cm")
    print(f"   Coral ends:       X = {coral_end*100:6.1f} cm")
    print(f"   Wave exit:        X = {wave_exit*100:6.1f} cm")
    print(f"   Upstream length:  {upstream_length*100:5.1f} cm ({upstream_length/domain_size['x']*100:4.1f}% of domain)")
    print(f"   Coral length:     {coral_length*100:5.1f} cm ({coral_length/domain_size['x']*100:4.1f}% of domain)")
    print(f"   Downstream length:{downstream_length*100:5.1f} cm ({downstream_length/domain_size['x']*100:4.1f}% of domain)")
    
    # Wave-coral interaction ratios
    water_depth = domain_bounds['z_max']
    wave_height = 0.16  # 16cm
    
    print(f"\n📊 WAVE-CORAL RATIOS:")
    print(f"   Water depth:      {water_depth*100:5.1f} cm")
    print(f"   Wave height:      {wave_height*100:5.1f} cm")
    print(f"   Coral height:     {coral_size['z']*100:5.1f} cm")
    print(f"   Coral/depth:      {coral_size['z']/water_depth*100:5.1f}%")
    print(f"   Wave/coral:       {wave_height/coral_size['z']:5.1f}×")
    print(f"   Submersion:       {coral_bounds['z_max']/water_depth*100:5.1f}% submerged")
    
    # Final verification
    all_fits = all(c > 0 for c in clearances.values())
    well_centered = abs(coral_center['x']) < 0.01 and abs(coral_center['y']) < 0.01
    realistic_size = 0.04 < coral_size['z'] < 0.06
    proper_position = 0.005 < coral_bounds['z_min'] and coral_bounds['z_max'] < water_depth
    
    print(f"\n✅ VERIFICATION CHECKLIST:")
    print(f"   Fits in domain:   {'✅ PASS' if all_fits else '❌ FAIL'}")
    print(f"   Well centered:    {'✅ PASS' if well_centered else '❌ FAIL'}")
    print(f"   Realistic size:   {'✅ PASS' if realistic_size else '❌ FAIL'}")
    print(f"   Proper position:  {'✅ PASS' if proper_position else '❌ FAIL'}")
    print(f"   Wave path clear:  {'✅ PASS' if upstream_length > 0 and downstream_length > 0 else '❌ FAIL'}")
    
    overall_correct = all_fits and well_centered and realistic_size and proper_position
    
    print(f"\n🏁 FINAL VERDICT:")
    if overall_correct:
        print(f"   DOMAIN SETUP: ✅ PERFECT - Ready for simulation")
        print(f"   • Coral positioned optimally for wave interaction")
        print(f"   • All clearances adequate for mesh generation")
        print(f"   • Cross-sections show proper 3D integration")
        print(f"   • Wave propagation path is clear and realistic")
    else:
        print(f"   DOMAIN SETUP: ⚠️ NEEDS ATTENTION")
    
    print("=" * 80)
    
    return overall_correct

def main():
    is_correct = analyze_cross_sections()
    
    if is_correct:
        print(f"\n🎯 SUMMARY: The initial domain is CORRECTLY configured!")
        print(f"   The BR01_branching.stl coral is perfectly positioned")
        print(f"   for realistic wave energy reduction simulation.")
    else:
        print(f"\n⚠️ SUMMARY: Domain configuration needs adjustment.")

if __name__ == "__main__":
    main()