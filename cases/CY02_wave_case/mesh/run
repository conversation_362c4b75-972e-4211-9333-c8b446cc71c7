#!/bin/bash
# OpenFOAM Run script generated by SnappyHexMesh GUI

function run_and_log(){
  # Run a command and redirect it's output to a log file.
  # First argument is the program name (log file name),
  # rest of the arguments contain the string to run the program.

  cmd=$1
  run_commands="${@:2}"
  echo "Running $cmd with command: $run_commands"
  $run_commands &> log."$cmd"
  if [ $? -ne 0 ]; then
    echo "Running $cmd failed, see log."$cmd". Exiting."
    exit 1
  fi
}

run_and_log blockMesh blockMesh
run_and_log surfaceFeatureExtract surfaceFeatureExtract
run_and_log snappyHexMesh snappyHexMesh
run_and_log checkMesh checkMesh -latestTime
# run_and_log postProcess postProcess -time '1:'
echo "Run done!"
