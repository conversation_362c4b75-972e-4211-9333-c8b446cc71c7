FoamFile
{
    version     2.0;
    format      ascii;
    class       dictionary;
    object      blockMeshDict;
}

// Exported by SnappyHexMesh GUI add-on for Blender v1.8
// Source file: C:\Users\<USER>\openFOAM - Coral Simulation\CorymboseCoralMesh.blend
// Export date: 2025-08-01 13:19:00.712382

scale 1;

vertices
(
    ( -0.3 -0.2 -0.1 )
    ( 1.2 -0.2 -0.1 )
    ( 1.2 0.2 -0.1 )
    ( -0.3 0.2 -0.1 )
    ( -0.3 -0.2 0.4 )
    ( 1.2 -0.2 0.4 ) 
    ( 1.2 0.2 0.4 ) 
    ( -0.3 0.2 0.4 ) 
);

blocks
(
    hex (0 1 2 3 4 5 6 7) (15 4 5) simpleGrading (1 1 1)
);

edges
(
);

boundary
(
    world
    {
        type patch;
        faces
        (
            (3 7 6 2)
            (0 4 7 3)
            (2 6 5 1)
            (1 5 4 0)
            (0 3 2 1)
            (4 5 6 7)
        );
    }
);
