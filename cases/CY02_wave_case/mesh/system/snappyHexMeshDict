FoamFile
{
    version         2;
    format          ascii;
    class           dictionary;
    object          snappyHexMeshDict;
}

// Exported by SnappyHexMesh GUI add-on for Blender v1.8
// Source file: C:\Users\<USER>\openFOAM - Coral Simulation\CorymboseCoralMesh.blend
// Export date: 2025-08-01 13:19:00.713354

// Details about SnappyHexMesh parameters can be found in annotated caseDicts:
// - For openfoam.org (development version), see
//   https://github.com/OpenFOAM/OpenFOAM-dev/blob/master/etc/caseDicts/annotated/snappyHexMeshDict
// - For openfoam.com (development version), see
//   https://develop.openfoam.com/Development/openfoam/-/blob/master/etc/caseDicts/annotated/snappyHexMeshDict
// See also links in https://openfoamwiki.net/index.php/SnappyHexMesh

// type            castellatedBufferLayer; // OpenFOAM.com v2412 option to enable buffer layer. Warning: Experimental feature!

castellatedMesh true;
snap            true;
addLayers       false;

mergePatchFaces false;  // Avoid face merging to increase layer coverage

geometry
{
    Coral
    {
        type triSurfaceMesh;
        file "Coral.stl";
        // Min Bounds = [-5.27184e-02 -4.36605e-02  1.99999e-03]
        // Max Bounds = [ 5.22048e-02  4.55283e-02  4.96049e-02]
        // Area = 3.88441e-02
    }
    Ground
    {
        type triSurfaceMesh;
        file "Ground.stl";
        // Min Bounds = [-2.00000e-01 -1.00000e-01  1.99999e-03]
        // Max Bounds = [ 1.10000e+00  1.00000e-01  1.99999e-03]
        // Area = 1.29439e-01
    }
    Inlet
    {
        type triSurfaceMesh;
        file "Inlet.stl";
        // Min Bounds = [-2.00000e-01 -1.00000e-01  1.99999e-03]
        // Max Bounds = [-2.00000e-01  1.00000e-01  3.02000e-01]
        // Area = 6.00000e-02
    }
    Outlet
    {
        type triSurfaceMesh;
        file "Outlet.stl";
        // Min Bounds = [ 1.10000e+00 -1.00000e-01  1.99999e-03]
        // Max Bounds = [ 1.10000e+00  1.00000e-01  3.02000e-01]
        // Area = 6.00000e-02
    }
    Wall
    {
        type triSurfaceMesh;
        file "Wall.stl";
        // Min Bounds = [-2.00000e-01 -1.00000e-01  1.99999e-03]
        // Max Bounds = [ 1.10000e+00  1.00000e-01  3.02000e-01]
        // Area = 1.17000e+00
    }
}

castellatedMeshControls
{
    maxLocalCells   100000;
    maxGlobalCells  10000000;
    minRefinementCells 10;
    maxLoadUnbalance 0.1;
    nCellsBetweenLevels 4;
    locationInMesh (0 0 0);
    allowFreeStandingZoneFaces true;
    resolveFeatureAngle 30;
    // useLeakClosure true; // OpenFOAM.com option
    handleSnapProblems true;
    useTopologicalSnapDetection true;

    features
    (
        {
            file "Coral.eMesh";
            level 0;
        }
        {
            file "Ground.eMesh";
            level 0;
        }
        {
            file "Inlet.eMesh";
            level 0;
        }
        {
            file "Outlet.eMesh";
            level 0;
        }
        {
            file "Wall.eMesh";
            level 0;
        }

    );

    refinementSurfaces
    {
        Coral
        {
            level (0 0);
            patchInfo { type wall; }
            addBufferLayers false;
        }
        Ground
        {
            level (0 0);
            patchInfo { type wall; }
            addBufferLayers false;
        }
        Inlet
        {
            level (0 0);
            patchInfo { type wall; }
            addBufferLayers false;
        }
        Outlet
        {
            level (0 0);
            patchInfo { type patch; }
            addBufferLayers false;
        }
        Wall
        {
            level (0 0);
            patchInfo { type wall; }
            addBufferLayers false;
        }

    }

    refinementRegions
    {

    }
}

snapControls
{
    nSmoothPatch 2;
    nSmoothInternal 1; // OpenFOAM.com option
    tolerance 2.0;
    nSolveIter 4;
    nRelaxIter 5;
    nFeatureSnapIter 3;
    implicitFeatureSnap false;
    explicitFeatureSnap true;
    multiRegionFeatureSnap true;
    nFaceSplitInterval -1; // OpenFOAM.com option
    releasePoints false;  // multi-region related option
    stringFeatures true;
    avoidDiagonal false;
    strictRegionSnap false;
    concaveAngle 45;
    minAreaRatio 0.3;

    // OpenFOAM.com v2412 buffer layer smoothing
    solver displacementPointSmoothing;
    displacementPointSmoothingCoeffs
    {
        pointSmoother           laplacian;
        nPointSmootherIter      10;
    }

}

addLayersControls
{
    // Layer sizing
    relativeSizes true;
    expansionRatio 1.3;
    finalLayerThickness 0.6;
    minThickness 0.001;
    nGrow 0;

    // Mesh dependencies
    featureAngle 85;
    mergePatchFacesAngle 45; // OpenFOAM.com option
    layerTerminationAngle 45; // OpenFOAM.com option
    maxFaceThicknessRatio 0.5;
    disableWallEdges false;

    // Mesh displacement iterations
    nSmoothSurfaceNormals 8;
    nSmoothThickness 2;
    nSmoothNormals 0;
    nSmoothDisplacement 12;
    nMedialAxisIter 1000;

    // Medial axis analysis
    minMedialAxisAngle 90;
    maxThicknessToMedialRatio 0.5;
    slipFeatureAngle 30;
    nRelaxIter 10;

    // OpenFOAM.com displacement motion solver
    // meshShrinker displacementMotionSolver;
    // solver displacementLaplacian;
    // displacementLaplacianCoeffs { diffusivity quadratic inverseDistance ("wall.*"); }

    // Mesh shrinking overall settings
    nBufferCellsNoExtrude 0;
    nLayerIter 8;
    nRelaxedIter 0;
    nOuterIter 1000; // OpenFOAM.com option, 1=single pass layer addition
    additionalReporting true;

    // OpenFOAM.com v2412 boundary layer smoothing
    solver displacementPointSmoothing;
    displacementPointSmoothingCoeffs
    {
        //pointSmoother           geometricElementTransform;
        //transformationParameter 0.667;
        //nPointSmootherIter      10;

        pointSmoother           laplacian;
        nPointSmootherIter      100;
    }

    layers
    {

    }
}

meshQualityControls
{
    nSmoothScale    4;
    errorReduction  0.75;
    #include "meshQualityDict"
}

writeFlags      ( scalarLevels layerSets layerFields );

mergeTolerance  1e-06;
