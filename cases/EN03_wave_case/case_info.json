{"coral_id": "EN03", "coral_name": "Encrusting Coral", "complexity": "LOW", "expected_wave_energy_reduction": "5-20%", "wave_parameters": {"height": "0.16 m", "period": "1.0 s", "depth": "0.16 m", "velocity": "0.16 m/s"}, "fluid_properties": {"seawater_density": "1023 kg/m³", "seawater_viscosity": "0.00097 Pa·s"}, "simulation_settings": {"time_step": "0.01 s", "simulation_time": "10 s", "solver": "interFoam"}, "run_sequence": ["blockMesh", "copy coral STL to constant/triSurface/coral.stl", "setFields -default", "snappyHexMesh -overwrite", "interFoam"]}