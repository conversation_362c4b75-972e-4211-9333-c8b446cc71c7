/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2.1.0                                 |
|   \\  /    A nd           | Web:      www.OpenFOAM.org                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       volVectorField;
    location    "0";
    object      U;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 1 -1 0 0 0 0];

internalField   uniform (0 0 0);

boundaryField
{
    inlet
    {
        type            uniformFixedValue;
        uniformValue    table
        (
            (0.0    (0.16 0 0))
            (0.25   (0.0 0 0))
            (0.5    (-0.16 0 0))
            (0.75   (0.0 0 0))
            (1.0    (0.16 0 0))
            (1.25   (0.0 0 0))
            (1.5    (-0.16 0 0))
            (1.75   (0.0 0 0))
            (2.0    (0.16 0 0))
            (2.25   (0.0 0 0))
            (2.5    (-0.16 0 0))
            (2.75   (0.0 0 0))
            (3.0    (0.16 0 0))
            (3.25   (0.0 0 0))
            (3.5    (-0.16 0 0))
            (3.75   (0.0 0 0))
            (4.0    (0.16 0 0))
            (4.25   (0.0 0 0))
            (4.5    (-0.16 0 0))
            (4.75   (0.0 0 0))
            (5.0    (0.16 0 0))
            (5.25   (0.0 0 0))
            (5.5    (-0.16 0 0))
            (5.75   (0.0 0 0))
            (6.0    (0.16 0 0))
            (6.25   (0.0 0 0))
            (6.5    (-0.16 0 0))
            (6.75   (0.0 0 0))
            (7.0    (0.16 0 0))
            (7.25   (0.0 0 0))
            (7.5    (-0.16 0 0))
            (7.75   (0.0 0 0))
            (8.0    (0.16 0 0))
            (8.25   (0.0 0 0))
            (8.5    (-0.16 0 0))
            (8.75   (0.0 0 0))
            (9.0    (0.16 0 0))
            (9.25   (0.0 0 0))
            (9.5    (-0.16 0 0))
            (9.75   (0.0 0 0))
            (10.0   (0.16 0 0))
        );
        value           uniform (0 0 0);
    }

    outlet
    {
        type            pressureInletOutletVelocity;
        value           uniform (0 0 0);
    }

    front
    {
        type            noSlip;
    }

    back
    {
        type            noSlip;
    }

    bottom
    {
        type            noSlip;
    }

    atmosphere
    {
        type            pressureInletOutletVelocity;
        value           uniform (0 0 0);
    }

}

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //