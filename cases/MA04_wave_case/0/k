/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2.1.0                                 |
|   \\  /    A nd           | Web:      www.OpenFOAM.org                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       volScalarField;
    location    "0";
    object      k;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 2 -2 0 0 0 0];

internalField   uniform 0.0004;

boundaryField
{
    inlet
    {
        type            fixedValue;
        value           uniform 0.0004;
    }

    outlet
    {
        type            zeroGradient;
    }

    front
    {
        type            kqRWallFunction;
        value           uniform 0.0004;
    }

    back
    {
        type            kqRWallFunction;
        value           uniform 0.0004;
    }

    bottom
    {
        type            kqRWallFunction;
        value           uniform 0.0004;
    }

    atmosphere
    {
        type            inletOutlet;
        inletValue      uniform 0.0004;
        value           uniform 0.0004;
    }

    coral
    {
        type            kqRWallFunction;
        value           uniform 0.0004;
    }
}

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //