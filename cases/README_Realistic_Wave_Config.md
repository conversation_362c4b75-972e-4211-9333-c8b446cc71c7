# Realistic Wave Simulation Configuration

## Overview
All 5 coral cases have been configured for realistic wave energy reduction studies with the following specifications:

## Wave Parameters (as per study requirements)
- **Wave Height (H)**: 0.16 m
- **Wave Period (T)**: 1.0 s  
- **Water Depth**: 0.16 m
- **Seawater Density**: 1,023 kg/m³
- **Seawater Viscosity**: 0.00097 Pa·s
- **Reference Velocity**: 0.16 m/s

## Simulation Settings
- **Time Step (ΔT)**: 0.01 s (adaptive)
- **Simulation Time**: 10 s (10 complete wave periods)
- **Mesh Resolution**: 60×60×20 cells (fine resolution for wave capture)
- **Solver**: interFoam (two-phase flow)
- **Turbulence Model**: k-omega SST

## Configured Cases
1. **BR01_wave_case** - Branching Coral (Expected: 40-60% energy reduction)
2. **CY02_wave_case** - Corymbose Coral (Expected: 30-45% energy reduction)
3. **EN03_wave_case** - Encrusting Coral (Expected: 5-20% energy reduction)
4. **MA04_wave_case** - Massive Coral (Expected: 15-30% energy reduction)  
5. **TB05_wave_case** - Table Coral (Expected: 25-40% energy reduction)

## Wave Boundary Conditions

### Oscillatory Velocity (U field)
- **Inlet**: Oscillatory flow with 1-second period
  - Forward flow: 0.16 m/s (0.0, 0.25, 1.0, 1.25... seconds)
  - Zero flow: (0.25, 0.75, 1.25, 1.75... seconds)  
  - Reverse flow: -0.16 m/s (0.5, 1.5, 2.5... seconds)
- **Outlet**: Pressure outlet
- **Coral surfaces**: No-slip walls
- **Atmosphere**: Pressure outlet

### Water Fraction (alpha.water field)
- **Inlet**: Oscillatory water level
  - High water (α=1.0): Wave crests
  - Medium water (α=0.8): Transition
  - Lower water (α=0.6): Wave troughs
- **Internal field**: Initially fully filled (α=1.0)

## Monitoring and Analysis

### Force Monitoring
- **Forces**: Total forces on coral surfaces
- **Force Coefficients**: Drag (Cd), Lift (Cl), Moments
- **Reference Values**: 
  - Velocity: 0.16 m/s
  - Length: 0.05 m (coral scale)
  - Area: 0.0025 m² (5cm × 5cm)

### Wave Gauges (3 locations)
- **Upstream**: x = -0.05 m (before coral)
- **At Coral**: x = 0.0 m (at coral location)
- **Downstream**: x = 0.05 m (after coral)

## Expected Results

### Primary Metrics
1. **Wave Energy Reduction (%)**: Main study objective
2. **Drag Coefficient (Cd)**: Flow resistance characterization
3. **Transmission Coefficient (Kt)**: Wave transmission ratio
4. **Energy Dissipation Rate**: Power loss per unit width

### Coral Performance Ranking (Predicted)
1. **Branching (BR01)**: Highest energy dissipation (complex 3D structure)
2. **Corymbose (CY02)**: High energy dissipation (branched with plates)
3. **Table (TB05)**: Moderate energy dissipation (horizontal deflection)
4. **Massive (MA04)**: Moderate energy dissipation (bulk obstruction)
5. **Encrusting (EN03)**: Lowest energy dissipation (minimal profile)

## Execution Instructions

### Quick Start
```bash
# Run all simulations (automated)
./run_wave_simulations.sh

# Run single case
./run_wave_simulations.sh BR01

# Analyze results
python3 analyze_wave_energy.py
```

### Manual Execution (single case)
```bash
cd EN03_wave_case  # or any case

# Step 1: Generate mesh
../openfoam.sh -- blockMesh

# Step 2: Initialize fields  
../openfoam.sh -- setFields

# Step 3: Generate coral geometry (optional)
../openfoam.sh -- snappyHexMesh -overwrite

# Step 4: Run simulation
../openfoam.sh -- interFoam
```

### Expected Runtime
- **Single case**: 30-60 minutes (depending on hardware)
- **All 5 cases**: 3-5 hours total
- **Mesh generation**: 2-5 minutes per case
- **Flow simulation**: 25-55 minutes per case

## Output Files

### Force Data
- `postProcessing/forces/0/forces.dat` - Force time series
- `postProcessing/forceCoeffs/0/forceCoeffs.dat` - Coefficient time series

### Wave Data  
- `postProcessing/waveGauges/*/surface*.raw` - Wave elevation at gauges
- Time directories (0.1, 0.2, 0.3...) - Flow field snapshots

### Analysis Results
- `wave_energy_analysis_results.csv` - Summary table
- `coral_wave_analysis.png` - Comparative plots

## Quality Assurance

### Mesh Quality Checks
- Courant number < 0.5 (adaptive time stepping)
- Non-orthogonality < 65°
- Aspect ratio < 10
- Minimum 20 cells per wave height

### Convergence Criteria
- Force coefficients stabilize after ~5 seconds
- Wave gauges show consistent periodic behavior
- Mass conservation residuals < 1e-6

## Troubleshooting

### Common Issues
1. **Mesh generation fails**: Use simpler coral geometry or coarser mesh
2. **Simulation diverges**: Reduce time step or increase mesh quality
3. **Long runtime**: Use fewer cells or shorter simulation time for testing
4. **Missing results**: Check log files for errors

### Performance Optimization
- Run on multi-core systems (decomposePar + parallel execution)
- Use SSD storage for faster I/O
- Monitor system resources during long runs

---

**Note**: This configuration provides a balance between physical realism and computational feasibility. For research-grade simulations, consider finer meshes, longer simulation times, and validation against experimental data.