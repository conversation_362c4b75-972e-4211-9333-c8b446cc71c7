/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2506                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    class       volScalarField;
    location    "0";
    object      alpha.water;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 0 0 0 0 0 0];

internalField   uniform 1;

boundaryField
{
    inlet
    {
        type            uniformFixedValue;
        uniformValue    table;
        uniformValueCoeffs
        {
            values          
41
(
(0 1)
(0.25 0.8)
(0.5 0.6)
(0.75 0.8)
(1 1)
(1.25 0.8)
(1.5 0.6)
(1.75 0.8)
(2 1)
(2.25 0.8)
(2.5 0.6)
(2.75 0.8)
(3 1)
(3.25 0.8)
(3.5 0.6)
(3.75 0.8)
(4 1)
(4.25 0.8)
(4.5 0.6)
(4.75 0.8)
(5 1)
(5.25 0.8)
(5.5 0.6)
(5.75 0.8)
(6 1)
(6.25 0.8)
(6.5 0.6)
(6.75 0.8)
(7 1)
(7.25 0.8)
(7.5 0.6)
(7.75 0.8)
(8 1)
(8.25 0.8)
(8.5 0.6)
(8.75 0.8)
(9 1)
(9.25 0.8)
(9.5 0.6)
(9.75 0.8)
(10 1)
)
;
        }
        value           uniform 1;
    }
    outlet
    {
        type            zeroGradient;
    }
    front
    {
        type            zeroGradient;
    }
    back
    {
        type            zeroGradient;
    }
    bottom
    {
        type            zeroGradient;
    }
    atmosphere
    {
        type            inletOutlet;
        inletValue      uniform 0;
        value           uniform 0;
    }
}


// ************************************************************************* //
