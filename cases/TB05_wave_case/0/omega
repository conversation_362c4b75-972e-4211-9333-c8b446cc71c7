/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2.1.0                                 |
|   \\  /    A nd           | Web:      www.OpenFOAM.org                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       volScalarField;
    location    "0";
    object      omega;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 0 -1 0 0 0 0];

internalField   uniform 0.07;

boundaryField
{
    inlet
    {
        type            fixedValue;
        value           uniform 0.07;
    }

    outlet
    {
        type            zeroGradient;
    }

    front
    {
        type            omegaWallFunction;
        value           uniform 0.07;
    }

    back
    {
        type            omegaWallFunction;
        value           uniform 0.07;
    }

    bottom
    {
        type            omegaWallFunction;
        value           uniform 0.07;
    }

    atmosphere
    {
        type            inletOutlet;
        inletValue      uniform 0.07;
        value           uniform 0.07;
    }

    coral
    {
        type            omegaWallFunction;
        value           uniform 0.07;
    }
}

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //