FoamFile
{
    version         2;
    format          ascii;
    class           dictionary;
    object          surfaceFeaturesDict;
}

// Exported by SnappyHexMesh GUI add-on for Blender v1.8
// Source file: C:\Users\<USER>\openFOAM - Coral Simulation\TableCoralMesh.blend
// Export date: 2025-08-01 13:35:59.776481


Coral.stl
{
    extractionMethod extractFromSurface;
    extractFromSurfaceCoeffs { includedAngle 150; }
    writeObj yes;
}

Ground.stl
{
    extractionMethod extractFromSurface;
    extractFromSurfaceCoeffs { includedAngle 150; }
    writeObj yes;
}

Inlet.stl
{
    extractionMethod extractFromSurface;
    extractFromSurfaceCoeffs { includedAngle 150; }
    writeObj yes;
}

Outlet.stl
{
    extractionMethod extractFromSurface;
    extractFromSurfaceCoeffs { includedAngle 150; }
    writeObj yes;
}

Wall.stl
{
    extractionMethod extractFromSurface;
    extractFromSurfaceCoeffs { includedAngle 150; }
    writeObj yes;
}



