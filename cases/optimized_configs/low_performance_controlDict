/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2.1.0                                 |
|   \\  /    A nd           | Web:      www.OpenFOAM.org                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       dictionary;
    location    "system";
    object      controlDict;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
// OPTIMIZED FOR LOW-SPEED COMPUTERS

application     interFoam;

startFrom       startTime;
startTime       0;
stopAt          endTime;

// PERFORMANCE OPTIMIZATION: Shorter simulation time
endTime         5;              // 5 seconds (5 wave periods) instead of 10

// PERFORMANCE OPTIMIZATION: Larger time steps
deltaT          0.01;           // 10x larger than original (0.001 vs 0.0001)

writeControl    adjustableRunTime;
writeInterval   0.2;            // Write every 0.2s (less frequent I/O)

purgeWrite      10;             // Keep only last 10 time steps (save disk space)
writeFormat     binary;         // Faster I/O than ASCII
writePrecision  6;              // Reduced precision (faster I/O)
writeCompression off;           // No compression (faster but larger files)

timeFormat      general;
timePrecision   6;
runTimeModifiable true;

// PERFORMANCE OPTIMIZATION: Relaxed time stepping
adjustTimeStep  yes;
maxCo           0.8;            // Higher Courant number (was 0.25)
maxAlphaCo      0.8;            // Higher interface Courant number (was 0.25)

minDeltaT       1e-6;           // Less restrictive minimum
maxDeltaT       0.02;           // Larger maximum time step (was 0.005)

// PERFORMANCE OPTIMIZATION: Simplified functions
functions
{
    forces
    {
        type            forces;
        libs            ("libforces.so");
        writeControl    timeStep;
        writeInterval   5;          // Write every 5 time steps (less frequent)
        patches         (coral);
        rho             rhoInf;
        rhoInf          1023;
        CofR            (0 0 0);
        log             false;      // Disable logging for performance
    }

    forceCoeffs
    {
        type            forceCoeffs;
        libs            ("libforces.so");
        writeControl    timeStep;
        writeInterval   5;          // Write every 5 time steps
        patches         (coral);
        rho             rhoInf;
        rhoInf          1023;
        liftDir         (0 0 1);
        dragDir         (1 0 0);
        CofR            (0 0 0);
        pitchAxis       (0 1 0);
        magUInf         0.16;
        lRef            0.05;
        Aref            0.0025;
        log             false;      // Disable logging for performance
    }
}

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
