/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2.1.0                                 |
|   \\  /    A nd           | Web:      www.OpenFOAM.org                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       dictionary;
    object      decomposeParDict;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
// OPTIMIZED FOR LOW-SPEED COMPUTERS

// PERFORMANCE OPTIMIZATION: Adjust based on available CPU cores
// For 2 cores: numberOfSubdomains 2;
// For 4 cores: numberOfSubdomains 4;
// For 6+ cores: numberOfSubdomains 4; (diminishing returns on small meshes)

numberOfSubdomains 2;           // Conservative for low-end hardware

// PERFORMANCE OPTIMIZATION: Use simple method for small meshes
method          simple;         // Faster than scotch for small cases

// Simple decomposition coefficients
simpleCoeffs
{
    n               (2 1 1);    // Split in X direction only
    delta           0.001;
}

// Alternative: scotch method (better load balancing but slower setup)
// Uncomment if you have 4+ cores and larger meshes
/*
method          scotch;

scotchCoeffs
{
    processorWeights
    (
        1
        1
        1
        1
    );
    writeGraph      false;      // Disable graph output for speed
}
*/

// Alternative: hierarchical method (good for structured meshes)
/*
method          hierarchical;

hierarchicalCoeffs
{
    n               (2 2 1);    // 2x2x1 decomposition for 4 cores
    delta           0.001;
    order           xyz;
}
*/

// Manual decomposition (not recommended for general use)
manualCoeffs
{
    dataFile        "cellDecomposition";
}

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

// USAGE NOTES:
// 1. For 1-2 CPU cores: Use numberOfSubdomains 1 (no decomposition)
// 2. For 2-4 CPU cores: Use numberOfSubdomains 2-4 with simple method
// 3. For 4+ CPU cores: Consider scotch method for better load balancing
// 4. Always test performance with different decomposition strategies
