/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2.1.0                                 |
|   \\  /    A nd           | Web:      www.OpenFOAM.org                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       dictionary;
    location    "system";
    object      fvSolution;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
// OPTIMIZED FOR LOW-SPEED COMPUTERS

solvers
{
    "alpha.water.*"
    {
        nAlphaCorr      2;          // Reduced from 3 (faster)
        nAlphaSubCycles 1;          // Reduced from 2 (faster)
        cAlpha          1;
        
        MULESCorr       yes;
        nLimiterIter    3;          // Reduced from 5 (faster)
        
        solver          smoothSolver;
        smoother        symGaussSeidel;
        tolerance       1e-7;       // Relaxed tolerance (was 1e-8)
        relTol          0.1;        // Higher relative tolerance
    }

    "pcorr.*"
    {
        solver          PCG;
        preconditioner  DIC;
        tolerance       1e-4;       // Relaxed tolerance
        relTol          0.1;
    }

    p_rgh
    {
        solver          PCG;
        preconditioner  DIC;
        tolerance       1e-6;       // Relaxed tolerance (was 1e-7)
        relTol          0.05;       // Higher relative tolerance
    }

    p_rghFinal
    {
        $p_rgh;
        relTol          0;
    }

    U
    {
        solver          smoothSolver;
        smoother        symGaussSeidel;
        tolerance       1e-5;       // Relaxed tolerance (was 1e-6)
        relTol          0.1;        // Higher relative tolerance
    }

    UFinal
    {
        $U;
        relTol          0;
    }
}

PIMPLE
{
    momentumPredictor   no;         // Disable for speed
    nOuterCorrectors    2;          // Reduced from 3 (faster)
    nCorrectors         2;          // Reduced from 3 (faster)
    nNonOrthogonalCorrectors 1;     // Reduced from 2 (faster)
    
    pRefCell        0;
    pRefValue       0;
}

relaxationFactors
{
    equations
    {
        ".*"            0.9;        // Higher relaxation (faster convergence)
    }
}

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
