# Simplified Coral Geometries for Low-Speed Computers

## Overview
For low-speed computers, complex coral STL files can be replaced with simplified geometric shapes that still capture the essential wave interaction characteristics.

## Geometry Options (in order of computational complexity)

### 1. ULTRA-SIMPLE: Basic Shapes (No snappyHexMesh needed)
- **Branching Coral (BR01)**: Multiple vertical cylinders
- **Corymbose Coral (CY02)**: Cylinder with horizontal plates
- **Table Coral (TB05)**: Single horizontal plate on cylinder
- **Massive Coral (MA04)**: Single large cylinder or box
- **Encrusting Coral (EN03)**: Low rectangular block

**Advantages**: 
- No mesh generation time
- Very fast simulation
- Easy to modify

**Implementation**: Use `topoSet` and `setFields` instead of snappyHexMesh

### 2. LOW-COMPLEXITY: Simplified STL Files
- Reduce STL triangle count by 90%
- Remove fine details
- Keep only major structural features

**Advantages**:
- Still uses realistic shapes
- Much faster snappyHexMesh
- Reasonable accuracy

### 3. MEDIUM-COMPLEXITY: Coarse STL Files
- Reduce STL triangle count by 70%
- Simplify small features
- Maintain overall coral structure

## Implementation Strategy

### For Ultra-Simple Geometries:
1. Skip snappyHexMesh entirely
2. Use `topoSet` to define coral regions
3. Use `setFields` to mark coral cells
4. Apply coral boundary conditions

### For Simplified STL Files:
1. Use mesh decimation tools (MeshLab, Blender)
2. Reduce triangle count significantly
3. Use coarser snappyHexMesh settings

## Performance Impact

| Geometry Type | Mesh Generation Time | Simulation Time | Accuracy |
|---------------|---------------------|-----------------|----------|
| Ultra-Simple  | < 1 minute         | 50% faster     | 70%      |
| Low-Complex   | 2-5 minutes        | 30% faster     | 85%      |
| Medium-Complex| 5-10 minutes       | 15% faster     | 95%      |
| Original      | 15-30 minutes      | Baseline       | 100%     |

## Recommended Approach
1. Start with Ultra-Simple for initial testing
2. Move to Low-Complex if more accuracy needed
3. Use Medium-Complex only if computational resources allow
