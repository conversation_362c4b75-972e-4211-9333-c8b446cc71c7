#!/bin/bash
# Optimized Wave Simulation Runner for Low-Speed Computers
# Usage: ./run_optimized_simulation.sh [case_name] [performance_level]
# Performance levels: ultra-low, low, medium, high

set -e  # Exit on any error

# Configuration
PERFORMANCE_LEVEL=${2:-"medium"}  # Default to medium performance
CASE_NAME=${1:-"BR01"}           # Default to BR01 case
CASE_DIR="${CASE_NAME}_wave_case"

# Performance configurations
declare -A MESH_CONFIGS
MESH_CONFIGS["ultra-low"]="20 20 8"    # 3,200 cells
MESH_CONFIGS["low"]="25 25 8"          # 5,000 cells  
MESH_CONFIGS["medium"]="30 30 10"      # 9,000 cells
MESH_CONFIGS["high"]="40 40 12"        # 19,200 cells

declare -A TIME_CONFIGS
TIME_CONFIGS["ultra-low"]="3"          # 3 seconds simulation
TIME_CONFIGS["low"]="4"                # 4 seconds simulation
TIME_CONFIGS["medium"]="5"             # 5 seconds simulation
TIME_CONFIGS["high"]="8"               # 8 seconds simulation

declare -A TIMESTEP_CONFIGS
TIMESTEP_CONFIGS["ultra-low"]="0.02"   # Large time step
TIMESTEP_CONFIGS["low"]="0.015"        # Medium-large time step
TIMESTEP_CONFIGS["medium"]="0.01"      # Medium time step
TIMESTEP_CONFIGS["high"]="0.005"       # Smaller time step

echo "========================================="
echo "Optimized Wave Simulation Runner"
echo "Performance Level: $PERFORMANCE_LEVEL"
echo "Case: $CASE_NAME"
echo "========================================="

# Validate inputs
if [[ ! " ultra-low low medium high " =~ " $PERFORMANCE_LEVEL " ]]; then
    echo "Error: Invalid performance level. Use: ultra-low, low, medium, high"
    exit 1
fi

if [ ! -d "$CASE_DIR" ]; then
    echo "Error: Case directory $CASE_DIR not found"
    exit 1
fi

cd "$CASE_DIR"

# Clean previous results
echo "Cleaning previous results..."
rm -rf [0-9]* processor* postProcessing log.* *.obj constant/polyMesh

# Apply optimized configurations
echo "Applying optimized configurations for $PERFORMANCE_LEVEL performance..."

# Update mesh resolution
MESH_RES=${MESH_CONFIGS[$PERFORMANCE_LEVEL]}
echo "  - Setting mesh resolution to: $MESH_RES cells"
cp ../optimized_configs/low_performance_blockMeshDict system/blockMeshDict
sed -i "s/hex (0 1 2 3 4 5 6 7) ([0-9]* [0-9]* [0-9]*)/hex (0 1 2 3 4 5 6 7) ($MESH_RES)/" system/blockMeshDict

# Update simulation time
SIM_TIME=${TIME_CONFIGS[$PERFORMANCE_LEVEL]}
echo "  - Setting simulation time to: $SIM_TIME seconds"
cp ../optimized_configs/low_performance_controlDict system/controlDict
sed -i "s/endTime.*$/endTime         $SIM_TIME;/" system/controlDict

# Update time step
TIMESTEP=${TIMESTEP_CONFIGS[$PERFORMANCE_LEVEL]}
echo "  - Setting time step to: $TIMESTEP seconds"
sed -i "s/deltaT.*$/deltaT          $TIMESTEP;/" system/controlDict

# Copy optimized solver settings
echo "  - Applying optimized solver settings"
cp ../optimized_configs/low_performance_fvSolution system/fvSolution

# Determine parallel processing
CPU_CORES=$(nproc)
if [ $CPU_CORES -le 2 ]; then
    PARALLEL=false
    echo "  - Running in serial mode (detected $CPU_CORES cores)"
else
    PARALLEL=true
    NPROC=$((CPU_CORES > 4 ? 4 : CPU_CORES))  # Cap at 4 for small meshes
    echo "  - Running in parallel mode with $NPROC processes (detected $CPU_CORES cores)"
    cp ../optimized_configs/low_performance_decomposeParDict system/decomposeParDict
    sed -i "s/numberOfSubdomains [0-9]*/numberOfSubdomains $NPROC/" system/decomposeParDict
fi

echo ""
echo "Starting simulation with optimized settings..."
echo "Estimated completion time: $(date -d "+$((SIM_TIME * 2)) minutes" "+%H:%M")"
echo ""

# Start performance monitor in background
echo "Starting performance monitor..."
python3 ../scripts/performance_monitor.py . &
MONITOR_PID=$!

# Trap to kill monitor on exit
trap "kill $MONITOR_PID 2>/dev/null || true" EXIT

# Step 1: Generate mesh
echo "Step 1/4: Generating optimized mesh..."
../openfoam.sh -c "blockMesh" > log.blockMesh 2>&1
if [ $? -ne 0 ]; then
    echo "Error: blockMesh failed. Check log.blockMesh"
    exit 1
fi

# Step 2: Initialize fields
echo "Step 2/4: Initializing fields..."
../openfoam.sh -c "setFields" > log.setFields 2>&1
if [ $? -ne 0 ]; then
    echo "Error: setFields failed. Check log.setFields"
    exit 1
fi

# Step 3: Generate coral geometry (if STL exists)
if [ -f "constant/triSurface/coral.stl" ]; then
    echo "Step 3/4: Generating coral geometry (simplified)..."
    # Use faster snappyHexMesh settings for low-end hardware
    ../openfoam.sh -c "snappyHexMesh -overwrite" > log.snappyHexMesh 2>&1
    if [ $? -ne 0 ]; then
        echo "Warning: snappyHexMesh failed, continuing with basic mesh"
    fi
else
    echo "Step 3/4: Skipping coral geometry (no STL file found)"
fi

# Step 4: Decompose for parallel (if needed)
if [ "$PARALLEL" = true ]; then
    echo "Step 4a/5: Decomposing mesh for parallel processing..."
    ../openfoam.sh -c "decomposePar" > log.decomposePar 2>&1
    if [ $? -ne 0 ]; then
        echo "Warning: decomposePar failed, running in serial"
        PARALLEL=false
    fi
fi

# Step 5: Run simulation
echo "Step $([ "$PARALLEL" = true ] && echo "5/5" || echo "4/4"): Running optimized wave simulation..."

if [ "$PARALLEL" = true ]; then
    timeout $((SIM_TIME * 300)) bash -c "../openfoam.sh -c \"mpirun -np $NPROC interFoam -parallel\" > log.interFoam 2>&1"
else
    timeout $((SIM_TIME * 300)) bash -c '../openfoam.sh -c "interFoam" > log.interFoam 2>&1'
fi

exit_code=$?

# Kill monitor
kill $MONITOR_PID 2>/dev/null || true

if [ $exit_code -eq 124 ]; then
    echo "Warning: Simulation timed out"
elif [ $exit_code -ne 0 ]; then
    echo "Error: Simulation failed. Check log.interFoam"
    exit 1
else
    echo "Simulation completed successfully!"
fi

# Reconstruct parallel results
if [ "$PARALLEL" = true ] && [ -d "processor0" ]; then
    echo "Reconstructing parallel results..."
    ../openfoam.sh -c "reconstructPar" > log.reconstructPar 2>&1
fi

echo ""
echo "========================================="
echo "Simulation Summary:"
echo "  Performance Level: $PERFORMANCE_LEVEL"
echo "  Mesh Resolution: $MESH_RES cells"
echo "  Simulation Time: $SIM_TIME seconds"
echo "  Time Step: $TIMESTEP seconds"
echo "  Parallel Processing: $PARALLEL"
echo "========================================="

cd ..
