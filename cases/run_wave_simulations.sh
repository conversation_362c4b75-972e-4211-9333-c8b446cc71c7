#!/bin/bash

# <PERSON><PERSON>t to run realistic wave simulations for all coral cases
# Usage: ./run_wave_simulations.sh [case_name]
# If no case_name provided, runs all cases

CASES=("BR01_wave_case" "CY02_wave_case" "EN03_wave_case" "MA04_wave_case" "TB05_wave_case")
CORAL_NAMES=("Branching" "Corymbose" "Encrusting" "Massive" "Table")

run_simulation() {
    local case_dir=$1
    local coral_name=$2
    
    echo "========================================="
    echo "Running simulation for $coral_name coral ($case_dir)"
    echo "========================================="
    
    if [ ! -d "$case_dir" ]; then
        echo "Error: Case directory $case_dir not found"
        return 1
    fi
    
    cd "$case_dir"
    
    # Clean previous results
    echo "Cleaning previous results..."
    rm -rf [0-9]* processor* postProcessing log.* *.obj
    
    # Step 1: Generate background mesh
    echo "Step 1/4: Generating background mesh..."
    ../openfoam.sh -c "blockMesh" > log.blockMesh 2>&1
    if [ $? -ne 0 ]; then
        echo "Error: blockMesh failed. Check log.blockMesh"
        cd ..
        return 1
    fi
    
    # Step 2: Initialize fields
    echo "Step 2/4: Initializing fields..."
    ../openfoam.sh -c "setFields" > log.setFields 2>&1
    if [ $? -ne 0 ]; then
        echo "Error: setFields failed. Check log.setFields"
        cd ..
        return 1
    fi
    
    # Step 3: Generate coral mesh (optional - may fail for complex geometries)
    echo "Step 3/4: Generating coral mesh (optional)..."
    ../openfoam.sh -c "snappyHexMesh -overwrite" > log.snappyHexMesh 2>&1
    if [ $? -ne 0 ]; then
        echo "Warning: snappyHexMesh failed. Continuing with basic mesh..."
        echo "Check log.snappyHexMesh for details"
    else
        echo "Coral mesh generated successfully"
    fi
    
    # Step 4: Run wave simulation
    echo "Step 4/4: Running wave simulation (10 seconds)..."
    echo "This may take 30-60 minutes depending on system performance..."
    
    # Run interFoam with timeout to prevent hanging
    timeout 7200 bash -c '../openfoam.sh -c "interFoam" > log.interFoam 2>&1'
    exit_code=$?
    
    if [ $exit_code -eq 124 ]; then
        echo "Warning: Simulation timed out after 2 hours"
    elif [ $exit_code -ne 0 ]; then
        echo "Error: interFoam failed. Check log.interFoam"
        cd ..
        return 1
    else
        echo "Simulation completed successfully!"
    fi
    
    # Check results
    if [ -d "postProcessing" ]; then
        echo ""
        echo "Results generated:"
        find postProcessing -name "*.dat" -o -name "*.raw" | head -5
        echo ""
        
        # Quick force analysis
        if [ -f "postProcessing/forceCoeffs/0/forceCoeffs.dat" ]; then
            avg_cd=$(awk 'NR>3 && $1>5 {cd+=$2; n++} END{if(n>0) print cd/n; else print "N/A"}' postProcessing/forceCoeffs/0/forceCoeffs.dat)
            echo "Average drag coefficient (last 5s): $avg_cd"
        fi
    fi
    
    cd ..
    echo "$coral_name coral simulation completed"
    echo ""
    return 0
}

# Main execution
if [ $# -eq 1 ]; then
    # Run single case
    case_name=$1
    found=0
    for i in "${!CASES[@]}"; do
        if [[ "${CASES[$i]}" == *"$case_name"* ]]; then
            run_simulation "${CASES[$i]}" "${CORAL_NAMES[$i]}"
            found=1
            break
        fi
    done
    if [ $found -eq 0 ]; then
        echo "Case not found. Available cases: ${CASES[*]}"
        exit 1
    fi
else
    # Run all cases
    echo "Starting batch execution of all coral wave simulations..."
    echo "Wave parameters: H=0.16m, T=1.0s, depth=0.16m, time=10s"
    echo ""
    
    start_time=$(date +%s)
    success_count=0
    
    for i in "${!CASES[@]}"; do
        if run_simulation "${CASES[$i]}" "${CORAL_NAMES[$i]}"; then
            ((success_count++))
        fi
    done
    
    end_time=$(date +%s)
    total_time=$((end_time - start_time))
    hours=$((total_time / 3600))
    minutes=$(((total_time % 3600) / 60))
    
    echo "========================================="
    echo "BATCH EXECUTION SUMMARY"
    echo "========================================="
    echo "Successful simulations: $success_count/${#CASES[@]}"
    echo "Total execution time: ${hours}h ${minutes}m"
    echo ""
    echo "Next steps for analysis:"
    echo "1. Check postProcessing/forceCoeffs/ for drag/lift data"
    echo "2. Check postProcessing/waveGauges/ for wave elevation"
    echo "3. Use paraFoam to visualize flow fields"
    echo "4. Calculate wave energy reduction from force data"
fi