#!/bin/bash

# Script to configure all coral cases for realistic wave simulation
# Usage: ./setup_realistic_cases.sh

CASES=("BR01_wave_case" "CY02_wave_case" "EN03_wave_case" "MA04_wave_case" "TB05_wave_case")

echo "Setting up realistic wave simulation configurations for all coral cases..."

for case in "${CASES[@]}"; do
    echo "Configuring case: $case"
    
    if [ ! -d "$case" ]; then
        echo "Warning: Case directory $case not found, skipping..."
        continue
    fi
    
    cd "$case"
    
    # Copy realistic wave boundary conditions from CY02 (already configured)
    echo "  - Setting up oscillatory wave boundary conditions"
    cp ../CY02_wave_case/0/U 0/
    cp ../CY02_wave_case/0/alpha.water 0/
    
    # Update mesh resolution for better wave capture
    echo "  - Updating mesh resolution to 60x60x20 cells"
    sed -i 's/hex (0 1 2 3 4 5 6 7) ([0-9]* [0-9]* [0-9]*)/hex (0 1 2 3 4 5 6 7) (60 60 20)/' system/blockMeshDict
    
    # Ensure 10-second simulation time
    echo "  - Setting simulation time to 10 seconds"
    sed -i 's/endTime.*$/endTime         10;              \/\/ 10 seconds (10 wave periods)/' system/controlDict
    
    # Add wave gauge monitoring if not present
    if ! grep -q "waveGauges" system/controlDict; then
        echo "  - Adding wave gauge monitoring"
        # Add wave gauges before the closing brace
        sed -i '/^}$/i\
\
    waveGauges\
    {\
        type            surfaces;\
        libs            ("libsampling.so");\
        writeControl    timeStep;\
        writeInterval   1;\
        setFormat       raw;\
        interpolationScheme cellPoint;\
        \
        fields          (alpha.water);\
        \
        surfaces\
        (\
            gaugeUpstream\
            {\
                type        plane;\
                basePoint   (-0.05 0 0.08);\
                normalVector (1 0 0);\
            }\
            gaugeCorals\
            {\
                type        plane;\
                basePoint   (0.0 0 0.08);\
                normalVector (1 0 0);\
            }\
            gaugeDownstream\
            {\
                type        plane;\
                basePoint   (0.05 0 0.08);\
                normalVector (1 0 0);\
            }\
        );\
    }' system/controlDict
    fi
    
    cd ..
    echo "  - Case $case configured successfully"
done

echo ""
echo "All cases configured for realistic wave simulation!"
echo ""
echo "Wave Parameters:"
echo "  - Wave height: 0.16 m"
echo "  - Wave period: 1.0 s"
echo "  - Water depth: 0.16 m"
echo "  - Simulation time: 10 s (10 wave periods)"
echo "  - Mesh resolution: 60x60x20 cells"
echo ""
echo "To run simulations:"
echo "  cd [case_directory]"
echo "  ../../openfoam.sh -- blockMesh"
echo "  ../../openfoam.sh -- setFields"
echo "  ../../openfoam.sh -- snappyHexMesh -overwrite  # (optional, for coral geometry)"
echo "  ../../openfoam.sh -- interFoam"
echo ""
echo "Results will include:"
echo "  - Force coefficients on coral (postProcessing/forceCoeffs/)"
echo "  - Wave elevation data (postProcessing/waveGauges/)"
echo "  - Flow field data for wave energy analysis"