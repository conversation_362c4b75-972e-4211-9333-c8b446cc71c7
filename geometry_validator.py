#!/usr/bin/env python3
"""
Coral Reef Geometry Validation and Preparation Tool
Validates STL files and mesh configuration for wave energy simulations
"""

import os
import struct
import numpy as np
import json
from pathlib import Path

class GeometryValidator:
    """Validates and analyzes coral reef geometry files"""
    
    def __init__(self, case_dir):
        self.case_dir = Path(case_dir)
        self.geom_dir = self.case_dir / "geom"
        self.mesh_dir = self.case_dir / "mesh"
        self.tri_surface_dir = self.mesh_dir / "constant" / "triSurface"
        
    def read_stl_binary(self, filename):
        """Read binary STL file and return vertices and triangle count"""
        try:
            with open(filename, 'rb') as f:
                header = f.read(80)
                num_triangles = struct.unpack('<I', f.read(4))[0]
                
                vertices = []
                for i in range(num_triangles):
                    f.read(12)  # Skip normal vector
                    v1 = struct.unpack('<3f', f.read(12))
                    v2 = struct.unpack('<3f', f.read(12))
                    v3 = struct.unpack('<3f', f.read(12))
                    vertices.extend([v1, v2, v3])
                    f.read(2)   # Skip attribute byte count
                
                return np.array(vertices), num_triangles
        except Exception as e:
            print(f"Error reading {filename}: {e}")
            return None, 0
    
    def analyze_geometry(self, vertices):
        """Analyze geometry bounds and properties"""
        if vertices is None or len(vertices) == 0:
            return None
            
        min_coords = np.min(vertices, axis=0)
        max_coords = np.max(vertices, axis=0)
        center = (min_coords + max_coords) / 2
        dimensions = max_coords - min_coords
        
        return {
            'min_coords': min_coords,
            'max_coords': max_coords,
            'center': center,
            'dimensions': dimensions,
            'volume_bbox': np.prod(dimensions)
        }
    
    def validate_stl_file(self, stl_path, expected_bounds=None):
        """Validate individual STL file"""
        if not stl_path.exists():
            return {'status': 'missing', 'file': str(stl_path)}
        
        vertices, num_triangles = self.read_stl_binary(stl_path)
        if vertices is None:
            return {'status': 'corrupted', 'file': str(stl_path)}
        
        geometry = self.analyze_geometry(vertices)
        if geometry is None:
            return {'status': 'invalid', 'file': str(stl_path)}
        
        result = {
            'status': 'valid',
            'file': str(stl_path),
            'triangles': num_triangles,
            'geometry': geometry
        }
        
        # Check expected bounds if provided
        if expected_bounds:
            for key, (min_val, max_val) in expected_bounds.items():
                if key in geometry:
                    value = geometry[key]
                    if isinstance(value, np.ndarray):
                        if not (min_val <= np.max(value) <= max_val):
                            result['warnings'] = result.get('warnings', [])
                            result['warnings'].append(f"{key} outside expected range")
        
        return result
    
    def validate_coral_geometry(self):
        """Validate coral-specific geometry requirements"""
        coral_stl = self.tri_surface_dir / "Coral.stl"
        
        if not coral_stl.exists():
            return {'status': 'missing', 'message': 'Coral.stl not found'}
        
        result = self.validate_stl_file(coral_stl)
        
        if result['status'] == 'valid':
            geom = result['geometry']
            height = geom['dimensions'][2]  # Z-dimension
            
            # Check coral height (should be ~5cm)
            if 0.04 <= height <= 0.06:
                result['coral_height_status'] = 'good'
            elif 0.02 <= height <= 0.08:
                result['coral_height_status'] = 'acceptable'
                result['warnings'] = result.get('warnings', [])
                result['warnings'].append(f"Coral height {height:.3f}m may need adjustment")
            else:
                result['coral_height_status'] = 'poor'
                result['warnings'] = result.get('warnings', [])
                result['warnings'].append(f"Coral height {height:.3f}m needs scaling")
        
        return result
    
    def validate_domain_geometry(self):
        """Validate domain boundaries"""
        required_files = ['Ground.stl', 'Inlet.stl', 'Outlet.stl', 'Wall.stl']
        results = {}
        
        for filename in required_files:
            stl_path = self.tri_surface_dir / filename
            results[filename] = self.validate_stl_file(stl_path)
        
        # Check domain dimensions
        if all(r['status'] == 'valid' for r in results.values()):
            # Combine all boundary geometries to check domain
            all_vertices = []
            for filename in required_files:
                vertices, _ = self.read_stl_binary(self.tri_surface_dir / filename)
                if vertices is not None:
                    all_vertices.extend(vertices)
            
            if all_vertices:
                domain_geom = self.analyze_geometry(np.array(all_vertices))
                results['domain_analysis'] = domain_geom
                
                # Check expected domain size
                dims = domain_geom['dimensions']
                expected_domain = {
                    'length': (1.2, 1.4),  # X-direction
                    'width': (0.18, 0.22),  # Y-direction  
                    'height': (0.25, 0.35)  # Z-direction
                }
                
                domain_ok = True
                if not (expected_domain['length'][0] <= dims[0] <= expected_domain['length'][1]):
                    domain_ok = False
                if not (expected_domain['width'][0] <= dims[1] <= expected_domain['width'][1]):
                    domain_ok = False
                if not (expected_domain['height'][0] <= dims[2] <= expected_domain['height'][1]):
                    domain_ok = False
                
                results['domain_status'] = 'good' if domain_ok else 'needs_check'
        
        return results
    
    def check_snappy_config(self):
        """Check SnappyHexMesh configuration"""
        snappy_dict = self.mesh_dir / "system" / "snappyHexMeshDict"
        
        if not snappy_dict.exists():
            return {'status': 'missing', 'message': 'snappyHexMeshDict not found'}
        
        try:
            with open(snappy_dict, 'r') as f:
                content = f.read()
            
            # Check for required geometry entries
            required_geometries = ['Coral', 'Ground', 'Inlet', 'Outlet', 'Wall']
            missing_geometries = []
            
            for geom in required_geometries:
                if geom not in content:
                    missing_geometries.append(geom)
            
            result = {
                'status': 'valid' if not missing_geometries else 'incomplete',
                'missing_geometries': missing_geometries
            }
            
            # Extract mesh parameters
            if 'maxLocalCells' in content:
                import re
                max_cells_match = re.search(r'maxLocalCells\s+(\d+)', content)
                if max_cells_match:
                    result['max_local_cells'] = int(max_cells_match.group(1))
            
            return result
            
        except Exception as e:
            return {'status': 'error', 'message': str(e)}
    
    def generate_report(self):
        """Generate comprehensive geometry validation report"""
        print("🌊 Coral Reef Geometry Validation Report")
        print("=" * 60)
        
        # Check directory structure
        print(f"\n📁 Directory Structure:")
        print(f"  Case directory: {self.case_dir}")
        print(f"  Geometry dir exists: {self.geom_dir.exists()}")
        print(f"  Mesh dir exists: {self.mesh_dir.exists()}")
        print(f"  TriSurface dir exists: {self.tri_surface_dir.exists()}")
        
        # Validate coral geometry
        print(f"\n🪸 Coral Geometry Validation:")
        coral_result = self.validate_coral_geometry()
        print(f"  Status: {coral_result['status']}")
        
        if coral_result['status'] == 'valid':
            geom = coral_result['geometry']
            print(f"  Triangles: {coral_result['triangles']:,}")
            print(f"  Dimensions: {geom['dimensions'][0]:.3f} × {geom['dimensions'][1]:.3f} × {geom['dimensions'][2]:.3f} m")
            print(f"  Height status: {coral_result.get('coral_height_status', 'unknown')}")
            
            if 'warnings' in coral_result:
                print(f"  ⚠️ Warnings:")
                for warning in coral_result['warnings']:
                    print(f"    - {warning}")
        else:
            print(f"  ❌ Issue: {coral_result.get('message', 'Unknown error')}")
        
        # Validate domain boundaries
        print(f"\n🏗️ Domain Boundary Validation:")
        domain_results = self.validate_domain_geometry()
        
        for filename, result in domain_results.items():
            if filename == 'domain_analysis' or filename == 'domain_status':
                continue
            print(f"  {filename}: {result['status']}")
            if result['status'] == 'valid':
                print(f"    Triangles: {result['triangles']:,}")
        
        if 'domain_status' in domain_results:
            print(f"  Overall domain: {domain_results['domain_status']}")
        
        # Check SnappyHexMesh configuration
        print(f"\n⚙️ SnappyHexMesh Configuration:")
        snappy_result = self.check_snappy_config()
        print(f"  Status: {snappy_result['status']}")
        
        if snappy_result['status'] == 'incomplete':
            print(f"  Missing geometries: {snappy_result['missing_geometries']}")
        elif snappy_result['status'] == 'valid':
            if 'max_local_cells' in snappy_result:
                print(f"  Max local cells: {snappy_result['max_local_cells']:,}")
        
        # Recommendations
        print(f"\n💡 Recommendations:")
        
        if coral_result['status'] != 'valid':
            print(f"  1. Fix coral geometry issues first")
        elif coral_result.get('coral_height_status') == 'poor':
            print(f"  1. Scale coral to ~5cm height")
        else:
            print(f"  1. ✅ Coral geometry looks good")
        
        domain_issues = sum(1 for r in domain_results.values() 
                          if isinstance(r, dict) and r.get('status') != 'valid')
        if domain_issues > 0:
            print(f"  2. Fix {domain_issues} domain boundary issues")
        else:
            print(f"  2. ✅ Domain boundaries look good")
        
        if snappy_result['status'] != 'valid':
            print(f"  3. Update SnappyHexMesh configuration")
        else:
            print(f"  3. ✅ SnappyHexMesh config ready")
        
        print(f"\n🚀 Next Steps:")
        if all([
            coral_result['status'] == 'valid',
            domain_issues == 0,
            snappy_result['status'] == 'valid'
        ]):
            print(f"  Ready for mesh generation! Run: cd {self.mesh_dir} && ./run")
        else:
            print(f"  Fix geometry issues before mesh generation")
        
        return {
            'coral': coral_result,
            'domain': domain_results,
            'snappy': snappy_result
        }

def main():
    """Main validation function"""
    import sys
    
    if len(sys.argv) != 2:
        print("Usage: python geometry_validator.py <case_directory>")
        print("Example: python geometry_validator.py cases/BR01_wave_case")
        sys.exit(1)
    
    case_dir = sys.argv[1]
    validator = GeometryValidator(case_dir)
    results = validator.generate_report()
    
    # Save results to JSON
    output_file = Path(case_dir) / "geometry_validation_report.json"
    with open(output_file, 'w') as f:
        # Convert numpy arrays to lists for JSON serialization
        def convert_numpy(obj):
            if isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, dict):
                return {k: convert_numpy(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy(item) for item in obj]
            return obj
        
        json.dump(convert_numpy(results), f, indent=2)
    
    print(f"\n📄 Detailed report saved to: {output_file}")

if __name__ == "__main__":
    main()
