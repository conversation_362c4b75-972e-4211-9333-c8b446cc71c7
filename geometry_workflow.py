#!/usr/bin/env python3
"""
Complete Geometry Workflow Automation for Coral Reef Wave Energy Simulations
Automates the entire geometry preparation and validation process
"""

import os
import sys
import argparse
import shutil
import subprocess
from pathlib import Path
import json

class GeometryWorkflow:
    """Complete workflow automation for coral geometry preparation"""
    
    def __init__(self, coral_type, base_dir="."):
        self.coral_type = coral_type
        self.base_dir = Path(base_dir)
        self.case_dir = self.base_dir / "cases" / f"{coral_type}_wave_case"
        self.geom_dir = self.case_dir / "geom"
        self.mesh_dir = self.case_dir / "mesh"
        
        # Coral type configurations
        self.coral_configs = {
            'EN03': {
                'name': 'Encrusting Coral',
                'complexity': 'LOW',
                'refinement_level': 0,
                'background_mesh': (10, 3, 4),
                'expected_cells': 3000
            },
            'MA04': {
                'name': 'Massive Coral',
                'complexity': 'MEDIUM',
                'refinement_level': 1,
                'background_mesh': (15, 4, 5),
                'expected_cells': 8000
            },
            'TB05': {
                'name': 'Table Coral',
                'complexity': 'MEDIUM',
                'refinement_level': 1,
                'background_mesh': (15, 4, 5),
                'expected_cells': 10000
            },
            'CY02': {
                'name': 'Corymbose Coral',
                'complexity': 'HIGH',
                'refinement_level': 1,
                'background_mesh': (20, 5, 6),
                'expected_cells': 15000
            },
            'BR01': {
                'name': 'Branching Coral',
                'complexity': 'HIGH',
                'refinement_level': 2,
                'background_mesh': (20, 6, 8),
                'expected_cells': 20000
            }
        }
    
    def create_directory_structure(self):
        """Create required directory structure"""
        print(f"📁 Creating directory structure for {self.coral_type}...")
        
        directories = [
            self.case_dir,
            self.geom_dir,
            self.mesh_dir,
            self.mesh_dir / "constant" / "triSurface",
            self.mesh_dir / "system",
            self.case_dir / "constant",
            self.case_dir / "system",
            self.case_dir / "0"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            print(f"  ✅ {directory}")
        
        return True
    
    def check_master_geometry(self):
        """Check if master geometry files exist"""
        print(f"🔍 Checking master geometry files...")
        
        # Look for master STL file
        master_stl_patterns = [
            f"{self.coral_type}_*.stl",
            f"*{self.coral_type.lower()}*.stl",
            f"*{self.coral_configs[self.coral_type]['name'].lower().replace(' ', '')}*.stl"
        ]
        
        master_files = []
        for pattern in master_stl_patterns:
            master_files.extend(self.geom_dir.glob(pattern))
        
        if master_files:
            print(f"  ✅ Found master geometry: {master_files[0]}")
            return master_files[0]
        else:
            print(f"  ⚠️ No master geometry found in {self.geom_dir}")
            print(f"  Expected patterns: {master_stl_patterns}")
            return None
    
    def validate_existing_geometry(self):
        """Validate existing geometry using geometry_validator.py"""
        print(f"🔍 Validating existing geometry...")
        
        try:
            # Import and use the validator
            sys.path.append(str(self.base_dir))
            from geometry_validator import GeometryValidator
            
            validator = GeometryValidator(self.case_dir)
            results = validator.generate_report()
            
            # Check if geometry is ready
            coral_valid = results['coral']['status'] == 'valid'
            domain_valid = all(r.get('status') == 'valid' for r in results['domain'].values() 
                             if isinstance(r, dict) and 'status' in r)
            snappy_valid = results['snappy']['status'] == 'valid'
            
            if coral_valid and domain_valid and snappy_valid:
                print(f"  ✅ All geometry validation passed!")
                return True
            else:
                print(f"  ⚠️ Geometry validation issues found:")
                if not coral_valid:
                    print(f"    - Coral geometry: {results['coral']['status']}")
                if not domain_valid:
                    print(f"    - Domain boundaries: issues detected")
                if not snappy_valid:
                    print(f"    - SnappyHexMesh config: {results['snappy']['status']}")
                return False
                
        except ImportError:
            print(f"  ⚠️ geometry_validator.py not found, skipping validation")
            return True
        except Exception as e:
            print(f"  ❌ Validation error: {e}")
            return False
    
    def generate_mesh(self):
        """Generate mesh using OpenFOAM tools"""
        print(f"🔧 Generating mesh for {self.coral_type}...")
        
        # Change to mesh directory
        original_dir = os.getcwd()
        os.chdir(self.mesh_dir)
        
        try:
            # Run mesh generation sequence
            commands = [
                "blockMesh",
                "surfaceFeatureExtract", 
                "snappyHexMesh -overwrite",
                "checkMesh -latestTime"
            ]
            
            for cmd in commands:
                print(f"  Running: {cmd}")
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                
                if result.returncode != 0:
                    print(f"  ❌ Command failed: {cmd}")
                    print(f"  Error: {result.stderr}")
                    return False
                else:
                    print(f"  ✅ {cmd} completed successfully")
            
            print(f"  ✅ Mesh generation completed!")
            return True
            
        except Exception as e:
            print(f"  ❌ Mesh generation error: {e}")
            return False
        finally:
            os.chdir(original_dir)
    
    def copy_to_simulation_case(self):
        """Copy generated mesh to main simulation case"""
        print(f"📋 Copying mesh to simulation case...")
        
        # Source mesh directory (latest time)
        mesh_source = self.mesh_dir
        time_dirs = [d for d in mesh_source.iterdir() 
                    if d.is_dir() and d.name.replace('.', '').isdigit()]
        
        if not time_dirs:
            print(f"  ⚠️ No mesh time directories found")
            return False
        
        latest_time = max(time_dirs, key=lambda x: float(x.name))
        
        # Copy mesh files
        mesh_files = ['points', 'faces', 'owner', 'neighbour', 'boundary']
        polyMesh_source = latest_time / "polyMesh"
        polyMesh_dest = self.case_dir / "constant" / "polyMesh"
        
        if polyMesh_source.exists():
            # Create destination
            polyMesh_dest.mkdir(parents=True, exist_ok=True)
            
            # Copy mesh files
            for mesh_file in mesh_files:
                source_file = polyMesh_source / mesh_file
                if source_file.exists():
                    shutil.copy2(source_file, polyMesh_dest)
                    print(f"  ✅ Copied {mesh_file}")
            
            print(f"  ✅ Mesh copied to simulation case")
            return True
        else:
            print(f"  ❌ polyMesh directory not found in {latest_time}")
            return False
    
    def create_case_info(self):
        """Create case information file"""
        config = self.coral_configs[self.coral_type]
        
        case_info = {
            'coral_type': self.coral_type,
            'coral_name': config['name'],
            'complexity': config['complexity'],
            'mesh_config': {
                'background_mesh': config['background_mesh'],
                'refinement_level': config['refinement_level'],
                'expected_cells': config['expected_cells']
            },
            'status': 'geometry_ready',
            'created_by': 'geometry_workflow.py'
        }
        
        info_file = self.case_dir / "case_info.json"
        with open(info_file, 'w') as f:
            json.dump(case_info, f, indent=2)
        
        print(f"  ✅ Case info saved to {info_file}")
    
    def run_complete_workflow(self):
        """Run the complete geometry preparation workflow"""
        print(f"🌊 Starting complete geometry workflow for {self.coral_type}")
        print(f"   Coral: {self.coral_configs[self.coral_type]['name']}")
        print("=" * 60)
        
        # Step 1: Create directory structure
        if not self.create_directory_structure():
            return False
        
        # Step 2: Check for master geometry
        master_geom = self.check_master_geometry()
        if not master_geom:
            print(f"\n❌ Workflow stopped: No master geometry found")
            print(f"📋 Next steps:")
            print(f"1. Create master geometry in Blender")
            print(f"2. Use SnappyHexMesh GUI to export components")
            print(f"3. Re-run this workflow")
            return False
        
        # Step 3: Validate existing geometry
        if not self.validate_existing_geometry():
            print(f"\n⚠️ Geometry validation issues detected")
            print(f"📋 Recommended actions:")
            print(f"1. Fix geometry issues in Blender")
            print(f"2. Re-export using SnappyHexMesh GUI")
            print(f"3. Re-run validation")
        
        # Step 4: Generate mesh (if components exist)
        components_exist = all((self.mesh_dir / "constant" / "triSurface" / f"{comp}.stl").exists() 
                             for comp in ['Coral', 'Ground', 'Inlet', 'Outlet', 'Wall'])
        
        if components_exist:
            if self.generate_mesh():
                # Step 5: Copy to simulation case
                self.copy_to_simulation_case()
            else:
                print(f"\n❌ Mesh generation failed")
                return False
        else:
            print(f"\n⚠️ Component STL files not found, skipping mesh generation")
            print(f"📋 Missing components - use SnappyHexMesh GUI to export:")
            for comp in ['Coral', 'Ground', 'Inlet', 'Outlet', 'Wall']:
                comp_file = self.mesh_dir / "constant" / "triSurface" / f"{comp}.stl"
                if not comp_file.exists():
                    print(f"  - {comp}.stl")
        
        # Step 6: Create case info
        self.create_case_info()
        
        print(f"\n🎉 Workflow completed for {self.coral_type}!")
        print(f"📁 Case directory: {self.case_dir}")
        
        return True
    
    def validate_only(self):
        """Run validation only"""
        print(f"🔍 Validating geometry for {self.coral_type}")
        print("=" * 60)
        
        if not self.case_dir.exists():
            print(f"❌ Case directory not found: {self.case_dir}")
            return False
        
        return self.validate_existing_geometry()

def main():
    """Main function with command line interface"""
    parser = argparse.ArgumentParser(description='Coral Reef Geometry Workflow Automation')
    parser.add_argument('--coral-type', required=True, 
                       choices=['EN03', 'MA04', 'TB05', 'CY02', 'BR01'],
                       help='Coral type to process')
    parser.add_argument('--create-new', action='store_true',
                       help='Create new case with complete workflow')
    parser.add_argument('--validate-only', action='store_true',
                       help='Only validate existing geometry')
    parser.add_argument('--base-dir', default='.',
                       help='Base directory (default: current directory)')
    
    args = parser.parse_args()
    
    # Create workflow instance
    workflow = GeometryWorkflow(args.coral_type, args.base_dir)
    
    if args.validate_only:
        success = workflow.validate_only()
    elif args.create_new:
        success = workflow.run_complete_workflow()
    else:
        print("Please specify either --create-new or --validate-only")
        return 1
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
