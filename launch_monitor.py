#!/usr/bin/env python3
"""
OpenFOAM Monitor GUI Launcher
Handles dependency installation and launches the monitoring GUI
"""

import sys
import subprocess
import importlib
import os

def check_and_install_dependencies():
    """Check for required dependencies and install if missing"""
    required_packages = [
        ('tkinter', None),  # Usually built-in
        ('matplotlib', 'matplotlib'),
        ('numpy', 'numpy'),
        ('queue', None),  # Built-in
    ]
    
    missing_packages = []
    
    for package_name, pip_name in required_packages:
        try:
            importlib.import_module(package_name)
            print(f"✓ {package_name} is available")
        except ImportError:
            if pip_name:
                missing_packages.append(pip_name)
                print(f"✗ {package_name} is missing")
    
    if missing_packages:
        print(f"\nInstalling missing packages: {', '.join(missing_packages)}")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install'] + missing_packages)
            print("✓ All dependencies installed successfully")
        except subprocess.CalledProcessError:
            print("✗ Failed to install dependencies. Please install manually:")
            print(f"pip install {' '.join(missing_packages)}")
            return False
    
    return True

def main():
    print("OpenFOAM Simulation Monitor")
    print("=" * 40)
    
    # Check if we're in the correct directory
    if not os.path.exists('cases') or not os.path.exists('openfoam.sh'):
        print("Error: Please run this script from the wave_energy_study root directory")
        print("Expected structure:")
        print("  wave_energy_study/")
        print("  ├── cases/")
        print("  ├── openfoam.sh")
        print("  └── utilities/python/openfoam_monitor_gui.py")
        sys.exit(1)
    
    # Check dependencies
    if not check_and_install_dependencies():
        sys.exit(1)
    
    # Launch the GUI
    try:
        from utilities.python.openfoam_monitor_gui import OpenFOAMMonitor
        import tkinter as tk
        
        print("\nLaunching GUI...")
        root = tk.Tk()
        app = OpenFOAMMonitor(root)
        root.mainloop()
        
    except ImportError as e:
        print(f"Error importing GUI: {e}")
        print("Make sure openfoam_monitor_gui.py is in utilities/python/")
        sys.exit(1)
    except Exception as e:
        print(f"Error launching GUI: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()