[21:57:46] Starting step: interfoam
[21:57:46] Running: interFoam
/bin/sh: 1: cd: can't cd to cases/None
[21:57:46] Error in step interfoam: Command failed with return code 2
[21:58:44] Starting step: cleanup
[21:58:44] Cleaned previous simulation data
[21:58:44] Completed step: cleanup
[21:58:45] Starting step: blockmesh
[21:58:45] Running: blockMesh
/bin/sh: 1: cd: can't cd to cases/None
[21:58:45] Error in step blockmesh: Command failed with return code 2
[21:58:46] Starting step: copy_stl
[21:58:46] Error in step copy_stl: 'NoneType' object is not subscriptable
[21:58:47] Starting step: setfields
[21:58:47] Running: setFields
/bin/sh: 1: cd: can't cd to cases/None
[21:58:47] Error in step setfields: Command failed with return code 2
[21:58:48] Starting step: snappyhex
[21:58:48] Running: snappyHexMesh -overwrite
/bin/sh: 1: cd: can't cd to cases/None
[21:58:48] Error in step snappyhex: Command failed with return code 2
[21:58:49] Starting step: checkmesh
[21:58:49] Running: checkMesh
/bin/sh: 1: cd: can't cd to cases/None
[21:58:49] Error in step checkmesh: Command failed with return code 2
[21:58:50] Starting step: interfoam
[21:58:50] Running: interFoam
/bin/sh: 1: cd: can't cd to cases/None
[21:58:50] Error in step interfoam: Command failed with return code 2

