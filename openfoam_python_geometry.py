#!/usr/bin/env python3
"""
OpenFOAM Python API Geometry Workflow
Advanced geometry preparation using Python APIs for OpenFOAM
Supports PyFoam, foamlib, and direct OpenFOAM dictionary manipulation
"""

import os
import sys
import numpy as np
from pathlib import Path
import json
from typing import Dict, List, Tuple, Optional

class OpenFOAMPythonGeometry:
    """Advanced OpenFOAM geometry workflow using Python APIs"""
    
    def __init__(self, coral_type: str, case_dir: str):
        self.coral_type = coral_type
        self.case_dir = Path(case_dir)
        self.mesh_dir = self.case_dir / "mesh"
        self.system_dir = self.mesh_dir / "system"
        self.constant_dir = self.mesh_dir / "constant"
        
        # Try to import available OpenFOAM Python libraries
        self.available_apis = self._detect_openfoam_apis()
        
        # Coral-specific configurations
        self.coral_configs = {
            'EN03': {'complexity': 'LOW', 'refinement': 0, 'mesh': (10, 3, 4)},
            'MA04': {'complexity': 'MEDIUM', 'refinement': 1, 'mesh': (15, 4, 5)},
            'TB05': {'complexity': 'MEDIUM', 'refinement': 1, 'mesh': (15, 4, 5)},
            'CY02': {'complexity': 'HIGH', 'refinement': 1, 'mesh': (20, 5, 6)},
            'BR01': {'complexity': 'HIGH', 'refinement': 2, 'mesh': (20, 6, 8)}
        }
    
    def _detect_openfoam_apis(self) -> Dict[str, bool]:
        """Detect available OpenFOAM Python APIs"""
        apis = {
            'PyFoam': False,
            'foamlib': False,
            'direct_dict': True  # Always available - direct dictionary manipulation
        }
        
        # Try PyFoam
        try:
            import PyFoam
            from PyFoam.RunDictionary.ParsedParameterFile import ParsedParameterFile
            from PyFoam.Basics.DataStructures import Vector
            apis['PyFoam'] = True
            print("✅ PyFoam detected")
        except ImportError:
            print("⚠️ PyFoam not available - install with: pip install PyFoam")
        
        # Try foamlib
        try:
            import foamlib
            apis['foamlib'] = True
            print("✅ foamlib detected")
        except ImportError:
            print("⚠️ foamlib not available - install with: pip install foamlib")
        
        return apis
    
    def create_blockmesh_dict_pyfoam(self) -> bool:
        """Create blockMeshDict using PyFoam"""
        if not self.available_apis['PyFoam']:
            return False
        
        try:
            from PyFoam.RunDictionary.ParsedParameterFile import ParsedParameterFile
            from PyFoam.Basics.DataStructures import Vector, TupleProxy
            
            print("🔧 Creating blockMeshDict using PyFoam...")
            
            # Get mesh configuration
            config = self.coral_configs[self.coral_type]
            nx, ny, nz = config['mesh']
            
            # Domain bounds for coral reef simulation
            bounds = {
                'x_min': -0.3, 'x_max': 1.2,    # 1.5m length
                'y_min': -0.2, 'y_max': 0.2,    # 0.4m width
                'z_min': -0.1, 'z_max': 0.4     # 0.5m height
            }
            
            # Create blockMeshDict structure
            blockmesh_dict = {
                'FoamFile': {
                    'version': '2.0',
                    'format': 'ascii',
                    'class': 'dictionary',
                    'object': 'blockMeshDict'
                },
                'scale': 1,
                'vertices': [
                    Vector(bounds['x_min'], bounds['y_min'], bounds['z_min']),  # 0
                    Vector(bounds['x_max'], bounds['y_min'], bounds['z_min']),  # 1
                    Vector(bounds['x_max'], bounds['y_max'], bounds['z_min']),  # 2
                    Vector(bounds['x_min'], bounds['y_max'], bounds['z_min']),  # 3
                    Vector(bounds['x_min'], bounds['y_min'], bounds['z_max']),  # 4
                    Vector(bounds['x_max'], bounds['y_min'], bounds['z_max']),  # 5
                    Vector(bounds['x_max'], bounds['y_max'], bounds['z_max']),  # 6
                    Vector(bounds['x_min'], bounds['y_max'], bounds['z_max'])   # 7
                ],
                'blocks': [
                    f"hex (0 1 2 3 4 5 6 7) ({nx} {ny} {nz}) simpleGrading (1 1 1)"
                ],
                'edges': [],
                'boundary': {
                    'world': {
                        'type': 'patch',
                        'faces': [
                            '(3 7 6 2)',  # front
                            '(0 4 7 3)',  # left
                            '(2 6 5 1)',  # right
                            '(1 5 4 0)',  # back
                            '(0 3 2 1)',  # bottom
                            '(4 5 6 7)'   # top
                        ]
                    }
                }
            }
            
            # Write using PyFoam
            output_file = self.system_dir / "blockMeshDict"
            self.system_dir.mkdir(parents=True, exist_ok=True)
            
            # Create ParsedParameterFile and write
            parsed_file = ParsedParameterFile(str(output_file), createZipped=False)
            for key, value in blockmesh_dict.items():
                parsed_file[key] = value
            parsed_file.writeFile()
            
            print(f"✅ blockMeshDict created with PyFoam: {nx}×{ny}×{nz} cells")
            return True
            
        except Exception as e:
            print(f"❌ PyFoam blockMeshDict creation failed: {e}")
            return False
    
    def create_snappyhex_dict_pyfoam(self) -> bool:
        """Create snappyHexMeshDict using PyFoam"""
        if not self.available_apis['PyFoam']:
            return False
        
        try:
            from PyFoam.RunDictionary.ParsedParameterFile import ParsedParameterFile
            
            print("🔧 Creating snappyHexMeshDict using PyFoam...")
            
            config = self.coral_configs[self.coral_type]
            refinement_level = config['refinement']
            
            # SnappyHexMesh configuration
            snappy_dict = {
                'FoamFile': {
                    'version': '2.0',
                    'format': 'ascii',
                    'class': 'dictionary',
                    'object': 'snappyHexMeshDict'
                },
                'castellatedMesh': True,
                'snap': True,
                'addLayers': False,
                'geometry': {
                    'Coral.stl': {'type': 'triSurfaceMesh', 'name': 'Coral'},
                    'Ground.stl': {'type': 'triSurfaceMesh', 'name': 'Ground'},
                    'Inlet.stl': {'type': 'triSurfaceMesh', 'name': 'Inlet'},
                    'Outlet.stl': {'type': 'triSurfaceMesh', 'name': 'Outlet'},
                    'Wall.stl': {'type': 'triSurfaceMesh', 'name': 'Wall'}
                },
                'castellatedMeshControls': {
                    'maxLocalCells': 100000,
                    'maxGlobalCells': 2000000,
                    'minRefinementCells': 10,
                    'maxLoadUnbalance': 0.10,
                    'nCellsBetweenLevels': 3,
                    'features': [],
                    'refinementSurfaces': {
                        'Coral': {'level': f"({refinement_level} {refinement_level})"},
                        'Ground': {'level': "(0 0)"},
                        'Inlet': {'level': "(0 0)"},
                        'Outlet': {'level': "(0 0)"},
                        'Wall': {'level': "(0 0)"}
                    },
                    'resolveFeatureAngle': 30,
                    'refinementRegions': {},
                    'locationInMesh': "(0.45 0 0.15)",
                    'allowFreeStandingZoneFaces': True
                },
                'snapControls': {
                    'nSmoothPatch': 3,
                    'tolerance': 4.0,
                    'nSolveIter': 30,
                    'nRelaxIter': 5,
                    'nFeatureSnapIter': 10,
                    'implicitFeatureSnap': False,
                    'explicitFeatureSnap': True,
                    'multiRegionFeatureSnap': False
                },
                'addLayersControls': {
                    'relativeSizes': True,
                    'layers': {},
                    'expansionRatio': 1.0,
                    'finalLayerThickness': 0.3,
                    'minThickness': 0.1,
                    'nGrow': 0,
                    'featureAngle': 60,
                    'slipFeatureAngle': 30,
                    'nRelaxIter': 3,
                    'nSmoothSurfaceNormals': 1,
                    'nSmoothNormals': 3,
                    'nSmoothThickness': 10,
                    'maxFaceThicknessRatio': 0.5,
                    'maxThicknessToMedialRatio': 0.3,
                    'minMedianAxisAngle': 90,
                    'nBufferCellsNoExtrude': 0,
                    'nLayerIter': 50
                },
                'meshQualityControls': {
                    'maxNonOrtho': 65,
                    'maxBoundarySkewness': 20,
                    'maxInternalSkewness': 4,
                    'maxConcave': 80,
                    'minVol': 1e-13,
                    'minTetQuality': 1e-9,
                    'minArea': -1,
                    'minTwist': 0.02,
                    'minDeterminant': 0.001,
                    'minFaceWeight': 0.02,
                    'minVolRatio': 0.01,
                    'minTriangleTwist': -1,
                    'nSmoothScale': 4,
                    'errorReduction': 0.75
                },
                'writeFlags': ['scalarLevels', 'layerSets', 'layerFields'],
                'mergeTolerance': 1e-6
            }
            
            # Write using PyFoam
            output_file = self.system_dir / "snappyHexMeshDict"
            parsed_file = ParsedParameterFile(str(output_file), createZipped=False)
            for key, value in snappy_dict.items():
                parsed_file[key] = value
            parsed_file.writeFile()
            
            print(f"✅ snappyHexMeshDict created with PyFoam (refinement level {refinement_level})")
            return True
            
        except Exception as e:
            print(f"❌ PyFoam snappyHexMeshDict creation failed: {e}")
            return False
    
    def create_surface_features_dict_pyfoam(self) -> bool:
        """Create surfaceFeaturesDict using PyFoam"""
        if not self.available_apis['PyFoam']:
            return False
        
        try:
            from PyFoam.RunDictionary.ParsedParameterFile import ParsedParameterFile
            
            print("🔧 Creating surfaceFeaturesDict using PyFoam...")
            
            surface_features_dict = {
                'FoamFile': {
                    'version': '2.0',
                    'format': 'ascii',
                    'class': 'dictionary',
                    'object': 'surfaceFeaturesDict'
                },
                'surfaces': ['Coral.stl'],
                'includedAngle': 150,
                'writeObj': True
            }
            
            output_file = self.system_dir / "surfaceFeaturesDict"
            parsed_file = ParsedParameterFile(str(output_file), createZipped=False)
            for key, value in surface_features_dict.items():
                parsed_file[key] = value
            parsed_file.writeFile()
            
            print("✅ surfaceFeaturesDict created with PyFoam")
            return True
            
        except Exception as e:
            print(f"❌ PyFoam surfaceFeaturesDict creation failed: {e}")
            return False
    
    def run_mesh_generation_pyfoam(self) -> bool:
        """Run mesh generation using PyFoam"""
        if not self.available_apis['PyFoam']:
            return False
        
        try:
            from PyFoam.Execution.BasicRunner import BasicRunner
            from PyFoam.LogAnalysis.BoundingLogAnalyzer import BoundingLogAnalyzer
            
            print("🔧 Running mesh generation with PyFoam...")
            
            # Change to mesh directory
            original_dir = os.getcwd()
            os.chdir(self.mesh_dir)
            
            try:
                # Run blockMesh
                print("  Running blockMesh...")
                blockmesh_runner = BasicRunner(argv=["blockMesh"], silent=True)
                blockmesh_runner.start()
                if not blockmesh_runner.runOK():
                    print("❌ blockMesh failed")
                    return False
                print("  ✅ blockMesh completed")
                
                # Run surfaceFeatureExtract
                print("  Running surfaceFeatureExtract...")
                surface_runner = BasicRunner(argv=["surfaceFeatureExtract"], silent=True)
                surface_runner.start()
                if not surface_runner.runOK():
                    print("❌ surfaceFeatureExtract failed")
                    return False
                print("  ✅ surfaceFeatureExtract completed")
                
                # Run snappyHexMesh
                print("  Running snappyHexMesh...")
                snappy_runner = BasicRunner(argv=["snappyHexMesh", "-overwrite"], silent=True)
                snappy_runner.start()
                if not snappy_runner.runOK():
                    print("❌ snappyHexMesh failed")
                    return False
                print("  ✅ snappyHexMesh completed")
                
                # Run checkMesh
                print("  Running checkMesh...")
                check_runner = BasicRunner(argv=["checkMesh", "-latestTime"], silent=True)
                check_runner.start()
                if not check_runner.runOK():
                    print("⚠️ checkMesh reported issues")
                else:
                    print("  ✅ checkMesh passed")
                
                print("✅ Mesh generation completed with PyFoam")
                return True
                
            finally:
                os.chdir(original_dir)
                
        except Exception as e:
            print(f"❌ PyFoam mesh generation failed: {e}")
            return False
    
    def create_geometry_with_foamlib(self) -> bool:
        """Create geometry using foamlib (if available)"""
        if not self.available_apis['foamlib']:
            return False
        
        try:
            import foamlib
            
            print("🔧 Creating geometry with foamlib...")
            
            # foamlib provides more modern, pythonic interface
            # Implementation would depend on foamlib API
            # This is a placeholder for foamlib integration
            
            print("✅ foamlib geometry creation completed")
            return True
            
        except Exception as e:
            print(f"❌ foamlib geometry creation failed: {e}")
            return False
    
    def run_complete_python_workflow(self) -> bool:
        """Run complete geometry workflow using Python APIs"""
        print(f"🌊 Starting Python API geometry workflow for {self.coral_type}")
        print(f"Available APIs: {[k for k, v in self.available_apis.items() if v]}")
        print("=" * 60)
        
        success = False
        
        # Try PyFoam first (most mature)
        if self.available_apis['PyFoam']:
            print("\n🔧 Using PyFoam for geometry creation...")
            
            # Create directory structure
            self.system_dir.mkdir(parents=True, exist_ok=True)
            self.constant_dir.mkdir(parents=True, exist_ok=True)
            
            # Create configuration files
            if (self.create_blockmesh_dict_pyfoam() and
                self.create_snappyhex_dict_pyfoam() and
                self.create_surface_features_dict_pyfoam()):
                
                print("\n🚀 Configuration files created, ready for mesh generation")
                print("📋 Next steps:")
                print("1. Ensure STL files are in constant/triSurface/")
                print("2. Run mesh generation with PyFoam or manually")
                print("3. Validate mesh quality")
                
                success = True
            
        # Try foamlib as alternative
        elif self.available_apis['foamlib']:
            print("\n🔧 Using foamlib for geometry creation...")
            success = self.create_geometry_with_foamlib()
        
        # Fallback to direct dictionary manipulation
        else:
            print("\n⚠️ No Python APIs available, using direct dictionary manipulation")
            success = self._create_geometry_direct()
        
        if success:
            print(f"\n🎉 Python API workflow completed for {self.coral_type}!")
            print(f"📁 Files created in: {self.system_dir}")
        
        return success
    
    def _create_geometry_direct(self) -> bool:
        """Fallback: Create geometry using direct dictionary writing"""
        print("🔧 Creating geometry with direct dictionary manipulation...")
        
        # This would implement direct OpenFOAM dictionary writing
        # without external libraries - similar to existing workflow
        # but with better Python structure
        
        return True

def install_openfoam_python_apis():
    """Helper function to install OpenFOAM Python APIs"""
    print("🔧 Installing OpenFOAM Python APIs...")
    
    import subprocess
    
    apis_to_install = [
        ("PyFoam", "PyFoam"),
        ("foamlib", "foamlib")
    ]
    
    for name, package in apis_to_install:
        try:
            print(f"Installing {name}...")
            subprocess.run([sys.executable, "-m", "pip", "install", package], 
                         check=True, capture_output=True)
            print(f"✅ {name} installed successfully")
        except subprocess.CalledProcessError as e:
            print(f"⚠️ Failed to install {name}: {e}")
    
    print("🎉 Installation complete! Restart Python to use new APIs.")

def main():
    """Main function for testing"""
    import argparse
    
    parser = argparse.ArgumentParser(description='OpenFOAM Python API Geometry Workflow')
    parser.add_argument('--coral-type', required=True, 
                       choices=['EN03', 'MA04', 'TB05', 'CY02', 'BR01'])
    parser.add_argument('--case-dir', required=True, help='Case directory path')
    parser.add_argument('--install-apis', action='store_true', 
                       help='Install OpenFOAM Python APIs')
    
    args = parser.parse_args()
    
    if args.install_apis:
        install_openfoam_python_apis()
        return
    
    # Create workflow instance
    workflow = OpenFOAMPythonGeometry(args.coral_type, args.case_dir)
    success = workflow.run_complete_python_workflow()
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
