#!/bin/bash

echo "=========================================="
echo "OpenFOAM Wave Energy Simulation Runner"
echo "=========================================="

# Function to run a simulation step
run_step() {
    local step_name="$1"
    local command="$2"
    
    echo ""
    echo "🚀 Step: $step_name"
    echo "----------------------------------------"
    
    if eval "$command"; then
        echo "✅ $step_name completed successfully"
        return 0
    else
        echo "❌ $step_name failed!"
        return 1
    fi
}

# Select case
echo ""
echo "Available cases:"
echo "1. BR01_wave_case (Branching)"
echo "2. CY02_wave_case (Corymbose)"
echo "3. EN03_wave_case (Encrusting)"
echo "4. MA04_wave_case (Massive)"
echo "5. TB05_wave_case (Table)"

read -p "Select case (1-5): " case_choice

case $case_choice in
    1) CASE="BR01_wave_case"; STL="BR01_branching.stl" ;;
    2) CASE="CY02_wave_case"; STL="CY02_corymbose.stl" ;;
    3) CASE="EN03_wave_case"; STL="EN03_encrusting.stl" ;;
    4) CASE="MA04_wave_case"; STL="MA04_massive.stl" ;;
    5) CASE="TB05_wave_case"; STL="TB05_table.stl" ;;
    *) echo "Invalid choice!"; exit 1 ;;
esac

echo ""
echo "Selected: $CASE"

# Check if case directory exists
if [ ! -d "cases/$CASE" ]; then
    echo "❌ Case directory not found: cases/$CASE"
    exit 1
fi

# Change to case directory
cd "cases/$CASE" || exit 1

echo ""
echo "📁 Working in: $(pwd)"

# Run simulation steps
echo ""
echo "🎯 Starting simulation sequence..."

# Step 1: Cleanup
run_step "Clean Previous Data" "rm -rf processor* 0.* [1-9]* postProcessing constant/polyMesh log.*" || exit 1

# Step 2: Generate base mesh
run_step "Generate Base Mesh" "../../openfoam.sh -- blockMesh" || exit 1

# Step 3: Copy coral geometry
run_step "Copy Coral Geometry" "mkdir -p constant/triSurface && cp geom/$STL constant/triSurface/coral.stl" || exit 1

# Step 4: Initialize fields
run_step "Initialize Fields" "../../openfoam.sh -- setFields" || exit 1

# Step 5: Generate coral mesh
run_step "Generate Coral Mesh" "../../openfoam.sh -- snappyHexMesh -overwrite" || exit 1

# Step 6: Check mesh quality
run_step "Check Mesh Quality" "../../openfoam.sh -- checkMesh" || exit 1

# Step 7: Run simulation
echo ""
echo "🏃 Starting main simulation (this may take a while)..."
echo "💡 You can monitor progress with: tail -f log.interFoam"
echo ""

run_step "Run Simulation" "../../openfoam.sh -- interFoam" || exit 1

echo ""
echo "🎉 Simulation completed successfully!"
echo ""
echo "📊 Results available in:"
echo "  - postProcessing/ - Force and coefficient data"
echo "  - Time directories (0.1, 0.2, etc.) - Flow field data"
echo ""
echo "🔍 To visualize results:"
echo "  paraview ${CASE}.foam"
echo ""
echo "📈 To analyze forces:"
echo "  python3 ../../utilities/python/coral_analysis.py"
echo ""