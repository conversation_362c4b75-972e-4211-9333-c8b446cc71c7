#!/bin/bash
"""
Unified OpenFOAM Case Management Script

This script provides standardized operations for all coral wave cases:
- Case setup and configuration
- Mesh generation
- Simulation execution
- Post-processing and analysis
- Batch operations across all cases
"""

set -e  # Exit on any error

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
CASES_DIR="$PROJECT_ROOT/cases"
TEMPLATES_DIR="$PROJECT_ROOT/templates"
UTILITIES_DIR="$PROJECT_ROOT/utilities"

# Coral case definitions
CORAL_CASES=("BR01_wave_case" "CY02_wave_case" "EN03_wave_case" "MA04_wave_case" "TB05_wave_case")
CORAL_NAMES=("Branching" "Corymbose" "Encrusting" "Massive" "Table")

# OpenFOAM Docker configuration
OPENFOAM_IMAGE="opencfd/openfoam-run:latest"
OPENFOAM_SCRIPT="$PROJECT_ROOT/openfoam.sh"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if case directory exists
check_case() {
    local case_name="$1"
    if [[ ! -d "$CASES_DIR/$case_name" ]]; then
        log_error "Case directory not found: $case_name"
        return 1
    fi
    return 0
}

# Check if OpenFOAM Docker script exists
check_openfoam_env() {
    if [[ ! -f "$OPENFOAM_SCRIPT" ]]; then
        log_error "OpenFOAM script not found: $OPENFOAM_SCRIPT"
        return 1
    fi
    return 0
}

# Apply template to case
apply_template() {
    local case_name="$1"
    local template_file="$2"
    local target_file="$3"
    
    if [[ ! -f "$TEMPLATES_DIR/$template_file" ]]; then
        log_error "Template not found: $template_file"
        return 1
    fi
    
    local case_dir="$CASES_DIR/$case_name"
    cp "$TEMPLATES_DIR/$template_file" "$case_dir/$target_file"
    log_success "Applied template $template_file to $case_name"
}

# Setup single case with templates
setup_case() {
    local case_name="$1"
    
    log_info "Setting up case: $case_name"
    
    if ! check_case "$case_name"; then
        return 1
    fi
    
    local case_dir="$CASES_DIR/$case_name"
    
    # Apply templates
    apply_template "$case_name" "controlDict.template" "system/controlDict"
    apply_template "$case_name" "waveProperties.template" "constant/waveProperties"
    apply_template "$case_name" "transportProperties.template" "constant/transportProperties"
    
    log_success "Case setup completed: $case_name"
}

# Run OpenFOAM command in case directory
run_openfoam_cmd() {
    local case_name="$1"
    shift
    local cmd="$@"
    
    if ! check_case "$case_name"; then
        return 1
    fi
    
    if ! check_openfoam_env; then
        return 1
    fi
    
    local case_dir="$CASES_DIR/$case_name"
    
    log_info "Running OpenFOAM command in $case_name: $cmd"
    
    cd "$case_dir"
    "$OPENFOAM_SCRIPT" -- $cmd
    
    if [[ $? -eq 0 ]]; then
        log_success "Command completed successfully in $case_name"
    else
        log_error "Command failed in $case_name"
        return 1
    fi
}

# Complete mesh generation for a case
generate_mesh() {
    local case_name="$1"
    
    log_info "Generating mesh for: $case_name"
    
    # Check if coral STL exists
    local case_dir="$CASES_DIR/$case_name"
    local coral_stl="$case_dir/constant/triSurface/coral.stl"
    
    if [[ ! -f "$coral_stl" ]]; then
        log_error "Coral STL not found: $coral_stl"
        return 1
    fi
    
    # Mesh generation sequence
    run_openfoam_cmd "$case_name" "blockMesh" || return 1
    run_openfoam_cmd "$case_name" "setFields -default" || return 1
    run_openfoam_cmd "$case_name" "snappyHexMesh -overwrite" || return 1
    run_openfoam_cmd "$case_name" "checkMesh" || return 1
    
    log_success "Mesh generation completed for: $case_name"
}

# Run simulation for a case
run_simulation() {
    local case_name="$1"
    
    log_info "Running simulation for: $case_name"
    
    # Initialize wave field
    run_openfoam_cmd "$case_name" "setWaves" || return 1
    
    # Run main simulation
    run_openfoam_cmd "$case_name" "interFoam" || return 1
    
    log_success "Simulation completed for: $case_name"
}

# Clean case directory
clean_case() {
    local case_name="$1"
    
    log_info "Cleaning case: $case_name"
    
    if ! check_case "$case_name"; then
        return 1
    fi
    
    local case_dir="$CASES_DIR/$case_name"
    
    cd "$case_dir"
    
    # Remove time directories except 0
    find . -maxdepth 1 -name "[0-9]*" -not -name "0" -type d -exec rm -rf {} + 2>/dev/null || true
    find . -maxdepth 1 -name "0.*" -type d -exec rm -rf {} + 2>/dev/null || true
    
    # Remove log files
    rm -f log.* 2>/dev/null || true
    
    # Remove postProcessing
    rm -rf postProcessing 2>/dev/null || true
    
    # Remove processor directories
    rm -rf processor* 2>/dev/null || true
    
    # Remove polyMesh except in constant
    find . -path "./constant" -prune -o -name "polyMesh" -type d -exec rm -rf {} + 2>/dev/null || true
    
    log_success "Case cleaned: $case_name"
}

# Status check for a case
check_case_status() {
    local case_name="$1"
    
    if ! check_case "$case_name"; then
        return 1
    fi
    
    local case_dir="$CASES_DIR/$case_name"
    
    echo "Case: $case_name"
    echo "  Directory: $case_dir"
    
    # Check for mesh
    if [[ -d "$case_dir/constant/polyMesh" ]] && [[ -f "$case_dir/constant/polyMesh/points" ]]; then
        echo "  Mesh: ✅ Present"
    else
        echo "  Mesh: ❌ Missing"
    fi
    
    # Check for coral STL
    if [[ -f "$case_dir/constant/triSurface/coral.stl" ]]; then
        echo "  Coral STL: ✅ Present"
    else
        echo "  Coral STL: ❌ Missing"
    fi
    
    # Check for simulation results
    local time_dirs=$(find "$case_dir" -maxdepth 1 -name "[0-9]*" -type d | wc -l)
    if [[ $time_dirs -gt 1 ]]; then
        echo "  Simulation: ✅ Results present ($time_dirs time directories)"
    else
        echo "  Simulation: ❌ No results"
    fi
    
    # Check for force data
    if [[ -f "$case_dir/postProcessing/forceCoeffs/0/forceCoeffs.dat" ]]; then
        echo "  Force data: ✅ Present"
    else
        echo "  Force data: ❌ Missing"
    fi
    
    echo ""
}

# Batch operations
batch_operation() {
    local operation="$1"
    shift
    local cases=("$@")
    
    if [[ ${#cases[@]} -eq 0 ]]; then
        cases=("${CORAL_CASES[@]}")
    fi
    
    log_info "Running batch operation '$operation' on cases: ${cases[*]}"
    
    local success_count=0
    local total_count=${#cases[@]}
    
    for case in "${cases[@]}"; do
        log_info "Processing case: $case"
        
        case "$operation" in
            "setup")
                setup_case "$case" && ((success_count++)) || log_error "Failed to setup $case"
                ;;
            "mesh")
                generate_mesh "$case" && ((success_count++)) || log_error "Failed to generate mesh for $case"
                ;;
            "simulate")
                run_simulation "$case" && ((success_count++)) || log_error "Failed to run simulation for $case"
                ;;
            "clean")
                clean_case "$case" && ((success_count++)) || log_error "Failed to clean $case"
                ;;
            "status")
                check_case_status "$case" && ((success_count++)) || log_error "Failed to check status for $case"
                ;;
            *)
                log_error "Unknown operation: $operation"
                return 1
                ;;
        esac
    done
    
    log_info "Batch operation completed: $success_count/$total_count cases processed successfully"
}

# Help function
show_help() {
    cat << EOF
Unified OpenFOAM Case Management Script

Usage: $0 <command> [options]

Commands:
  setup <case>          - Setup case with templates
  mesh <case>          - Generate mesh for case
  simulate <case>      - Run simulation for case
  clean <case>         - Clean case directory
  status <case>        - Show case status
  
  batch-setup          - Setup all cases
  batch-mesh           - Generate mesh for all cases
  batch-simulate       - Run simulations for all cases
  batch-clean          - Clean all cases
  batch-status         - Show status for all cases
  
  list                 - List available cases
  analyze              - Run unified analysis
  
  help                 - Show this help

Cases:
  BR01_wave_case       - Branching coral
  CY02_wave_case       - Corymbose coral
  EN03_wave_case       - Encrusting coral
  MA04_wave_case       - Massive coral
  TB05_wave_case       - Table coral

Examples:
  $0 setup BR01_wave_case
  $0 mesh BR01_wave_case
  $0 simulate BR01_wave_case
  $0 batch-status
  $0 analyze

EOF
}

# Main command processing
main() {
    if [[ $# -eq 0 ]]; then
        show_help
        exit 1
    fi
    
    local command="$1"
    shift
    
    case "$command" in
        "setup")
            if [[ $# -ne 1 ]]; then
                log_error "Usage: $0 setup <case>"
                exit 1
            fi
            setup_case "$1"
            ;;
        "mesh")
            if [[ $# -ne 1 ]]; then
                log_error "Usage: $0 mesh <case>"
                exit 1
            fi
            generate_mesh "$1"
            ;;
        "simulate")
            if [[ $# -ne 1 ]]; then
                log_error "Usage: $0 simulate <case>"
                exit 1
            fi
            run_simulation "$1"
            ;;
        "clean")
            if [[ $# -ne 1 ]]; then
                log_error "Usage: $0 clean <case>"
                exit 1
            fi
            clean_case "$1"
            ;;
        "status")
            if [[ $# -ne 1 ]]; then
                log_error "Usage: $0 status <case>"
                exit 1
            fi
            check_case_status "$1"
            ;;
        "batch-setup")
            batch_operation "setup" "$@"
            ;;
        "batch-mesh")
            batch_operation "mesh" "$@"
            ;;
        "batch-simulate")
            batch_operation "simulate" "$@"
            ;;
        "batch-clean")
            batch_operation "clean" "$@"
            ;;
        "batch-status")
            batch_operation "status" "$@"
            ;;
        "list")
            log_info "Available coral cases:"
            for i in "${!CORAL_CASES[@]}"; do
                local case="${CORAL_CASES[$i]}"
                local name="${CORAL_NAMES[$i]}"
                local status="❌"
                if [[ -d "$CASES_DIR/$case" ]]; then
                    status="✅"
                fi
                echo "  $status $case - $name"
            done
            ;;
        "analyze")
            log_info "Running unified analysis..."
            if [[ -f "$UTILITIES_DIR/python/coral_analysis.py" ]]; then
                cd "$PROJECT_ROOT"
                python3 "$UTILITIES_DIR/python/coral_analysis.py"
            else
                log_error "Analysis script not found"
                exit 1
            fi
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "Unknown command: $command"
            show_help
            exit 1
            ;;
    esac
}

# Execute main function
main "$@"