#!/usr/bin/env python3
"""
Performance Monitor for OpenFOAM Wave Simulations
Optimized for low-speed computers
"""

import os
import time
import re
import sys
from datetime import datetime, timedelta
import subprocess

class SimulationMonitor:
    def __init__(self, case_dir):
        self.case_dir = case_dir
        self.log_file = os.path.join(case_dir, "log.interFoam")
        self.start_time = None
        self.current_time = 0.0
        self.end_time = 10.0
        self.last_check = time.time()
        
    def read_control_dict(self):
        """Read simulation parameters from controlDict"""
        control_dict = os.path.join(self.case_dir, "system", "controlDict")
        if os.path.exists(control_dict):
            with open(control_dict, 'r') as f:
                content = f.read()
                # Extract endTime
                match = re.search(r'endTime\s+([0-9.]+)', content)
                if match:
                    self.end_time = float(match.group(1))
    
    def parse_log_file(self):
        """Parse the OpenFOAM log file for progress"""
        if not os.path.exists(self.log_file):
            return None
            
        try:
            with open(self.log_file, 'r') as f:
                lines = f.readlines()
                
            # Look for time steps
            for line in reversed(lines[-50:]):  # Check last 50 lines
                # Match time step lines like "Time = 1.5"
                match = re.search(r'Time = ([0-9.]+)', line)
                if match:
                    self.current_time = float(match.group(1))
                    return True
                    
            return False
        except Exception as e:
            print(f"Error reading log file: {e}")
            return False
    
    def estimate_completion(self):
        """Estimate simulation completion time"""
        if self.current_time <= 0:
            return "Unknown"
            
        progress = self.current_time / self.end_time
        if progress <= 0:
            return "Unknown"
            
        if self.start_time is None:
            self.start_time = time.time()
            
        elapsed = time.time() - self.start_time
        total_estimated = elapsed / progress
        remaining = total_estimated - elapsed
        
        if remaining < 0:
            return "Almost done"
            
        return str(timedelta(seconds=int(remaining)))
    
    def get_system_info(self):
        """Get basic system performance info"""
        try:
            # CPU usage
            cpu_info = subprocess.run(['top', '-bn1'], capture_output=True, text=True)
            cpu_lines = cpu_info.stdout.split('\n')[:5]
            
            # Memory usage
            mem_info = subprocess.run(['free', '-h'], capture_output=True, text=True)
            mem_lines = mem_info.stdout.split('\n')[:3]
            
            return {
                'cpu': cpu_lines,
                'memory': mem_lines
            }
        except:
            return None
    
    def display_status(self):
        """Display current simulation status"""
        os.system('clear')  # Clear screen
        
        print("=" * 60)
        print("OpenFOAM Wave Simulation Monitor")
        print("Optimized for Low-Speed Computers")
        print("=" * 60)
        print(f"Case Directory: {self.case_dir}")
        print(f"Current Time: {datetime.now().strftime('%H:%M:%S')}")
        print("-" * 60)
        
        if self.parse_log_file():
            progress = (self.current_time / self.end_time) * 100
            progress_bar = "█" * int(progress / 2) + "░" * (50 - int(progress / 2))
            
            print(f"Simulation Progress:")
            print(f"  Time: {self.current_time:.3f} / {self.end_time:.1f} seconds")
            print(f"  Progress: [{progress_bar}] {progress:.1f}%")
            print(f"  Estimated completion: {self.estimate_completion()}")
        else:
            print("Simulation not started or log file not found")
            
        print("-" * 60)
        
        # System info
        sys_info = self.get_system_info()
        if sys_info:
            print("System Status:")
            if sys_info['cpu']:
                for line in sys_info['cpu'][:2]:
                    if 'Cpu' in line or 'load average' in line:
                        print(f"  {line.strip()}")
            if sys_info['memory']:
                for line in sys_info['memory'][1:2]:
                    print(f"  {line.strip()}")
        
        print("-" * 60)
        print("Press Ctrl+C to exit monitor")
        print("=" * 60)
    
    def monitor(self, update_interval=10):
        """Main monitoring loop"""
        self.read_control_dict()
        
        try:
            while True:
                self.display_status()
                time.sleep(update_interval)
                
                # Check if simulation is complete
                if self.current_time >= self.end_time:
                    print("\nSimulation completed!")
                    break
                    
        except KeyboardInterrupt:
            print("\nMonitoring stopped by user")

def main():
    if len(sys.argv) != 2:
        print("Usage: python3 performance_monitor.py <case_directory>")
        print("Example: python3 performance_monitor.py cases/BR01_wave_case")
        sys.exit(1)
    
    case_dir = sys.argv[1]
    if not os.path.exists(case_dir):
        print(f"Error: Case directory '{case_dir}' not found")
        sys.exit(1)
    
    monitor = SimulationMonitor(case_dir)
    monitor.monitor()

if __name__ == "__main__":
    main()
