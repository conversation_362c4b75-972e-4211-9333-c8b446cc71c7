/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2.1.0                                 |
|   \\  /    A nd           | Web:      www.OpenFOAM.org                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       dictionary;
    location    "constant";
    object      transportProperties;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

phases (water air);

water
{
    transportModel  Newtonian;
    nu              9.48e-07;    // Seawater kinematic viscosity (0.00097 Pa·s / 1023 kg/m³)
    rho             1023;        // Seawater density kg/m³
}

air
{
    transportModel  Newtonian;
    nu              1.48e-05;    // Air kinematic viscosity
    rho             1.225;       // Air density kg/m³
}

sigma           0.0728;          // Air-seawater surface tension N/m

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //