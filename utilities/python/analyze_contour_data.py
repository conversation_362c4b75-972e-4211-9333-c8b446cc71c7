#!/usr/bin/env python3
"""
Simple longitudinal cross-section visualization for OpenFOAM coral simulation results
Uses Python/matplotlib to create basic contour plots
"""

import numpy as np
import matplotlib.pyplot as plt
import os
import sys

def read_openfoam_field_header(field_file):
    """Read OpenFOAM field file header to get mesh dimensions"""
    try:
        with open(field_file, 'r') as f:
            lines = f.readlines()
            
        # Look for internalField section
        for i, line in enumerate(lines):
            if 'internalField' in line and 'uniform' in line:
                # Simple uniform field
                return None, None
            elif 'internalField' in line and 'nonuniform' in line:
                # Non-uniform field data
                next_line = lines[i+1].strip()
                if next_line.isdigit():
                    n_cells = int(next_line)
                    return n_cells, None
        
        return None, None
    except Exception as e:
        print(f"Error reading field header: {e}")
        return None, None

def create_simple_field_summary(case_dir):
    """Create a simple field data summary"""
    time_dirs = [d for d in os.listdir(case_dir) if d.replace('.', '').isdigit()]
    if not time_dirs:
        print("No time directories found")
        return
    
    latest_time = sorted(time_dirs, key=float)[-1]
    time_path = os.path.join(case_dir, latest_time)
    
    print(f"FIELD DATA SUMMARY - Time: {latest_time} s")
    print("=" * 50)
    
    field_files = ['alpha.water', 'U', 'p_rgh', 'k']
    
    for field in field_files:
        field_path = os.path.join(time_path, field)
        if os.path.exists(field_path):
            try:
                # Get file size as indicator of data amount
                file_size = os.path.getsize(field_path)
                n_cells, _ = read_openfoam_field_header(field_path)
                
                print(f"\n{field}:")
                print(f"  File size: {file_size/1024:.1f} KB")
                if n_cells:
                    print(f"  Mesh cells: {n_cells:,}")
                
                # Try to read a sample of the data
                with open(field_path, 'r') as f:
                    lines = f.readlines()
                
                # Look for data section
                data_started = False
                sample_values = []
                for line in lines:
                    if '(' in line and ')' in line and data_started:
                        # Vector data
                        try:
                            clean_line = line.strip('()\n ')
                            values = [float(x) for x in clean_line.split()]
                            if len(values) == 3:  # Vector field
                                magnitude = np.sqrt(sum(v**2 for v in values))
                                sample_values.append(magnitude)
                            elif len(values) == 1:  # Scalar field
                                sample_values.append(values[0])
                        except:
                            continue
                    elif line.strip().replace('.', '').replace('-', '').replace('+', '').replace('e', '').isdigit():
                        # Scalar data
                        try:
                            sample_values.append(float(line.strip()))
                        except:
                            continue
                    elif 'internalField' in line:
                        data_started = True
                    
                    if len(sample_values) >= 100:  # Sample first 100 values
                        break
                
                if sample_values:
                    print(f"  Data range: {min(sample_values):.4f} to {max(sample_values):.4f}")
                    print(f"  Average: {np.mean(sample_values):.4f}")
            
            except Exception as e:
                print(f"  Error reading {field}: {e}")
        else:
            print(f"\n{field}: NOT FOUND")

def create_paraview_instructions(case_dir):
    """Create detailed ParaView instructions for manual visualization"""
    
    instructions = f"""
PARAVIEW LONGITUDINAL CROSS-SECTION INSTRUCTIONS
================================================

Case: {os.path.basename(case_dir)}

STEP-BY-STEP PARAVIEW WORKFLOW:
==============================

1. OPEN CASE:
   paraFoam -case {case_dir}
   (This will launch ParaView with the case loaded)

2. LOAD DATA:
   - In Properties panel, check all desired fields:
     ✓ alpha.water (wave field)
     ✓ U (velocity)
     ✓ p_rgh (pressure)
     ✓ k (turbulent kinetic energy)
   - Click "Apply"
   - Go to final time step (t=0.2)

3. CREATE LONGITUDINAL SLICE:
   - Filters → Alphabetical → Slice
   - In Properties:
     * Origin: [0.2, 0.0, 0.0] (domain center)
     * Normal: [1, 0, 0] (X-direction normal)
   - Click "Apply"

4. VISUALIZATION SETTINGS:
   - Hide original 3D object (eye icon)
   - Select slice in Pipeline Browser
   - In Properties → Coloring, choose field:

   For WAVE FIELD (alpha.water):
   - Color by: alpha.water
   - Color scale: 0.0 (air) to 1.0 (water)
   - Use "Cool to Warm" colormap

   For VELOCITY FIELD:
   - Color by: U (magnitude)
   - Use "Rainbow" colormap
   - Shows flow patterns around coral

   For PRESSURE FIELD:
   - Color by: p_rgh
   - Use "Blue to Red Rainbow" colormap
   - Shows pressure variations

   For TURBULENCE:
   - Color by: k
   - Use "Viridis" colormap
   - Shows energy dissipation zones

5. SAVE IMAGES:
   - File → Save Screenshot
   - Choose high resolution (1920x1080)
   - Save as PNG format

ALTERNATIVE: COMMAND LINE VISUALIZATION
======================================

If pvpython is available:
cd {case_dir}
pvpython generate_plots.py

WHAT TO LOOK FOR IN CONTOURS:
============================
- Wave height reduction downstream of coral
- Velocity acceleration around coral edges
- Pressure buildup on coral upstream face
- Turbulent wake region behind coral
- Energy dissipation patterns

EXPECTED RESULTS FOR BR01 (BRANCHING):
=====================================
- Complex flow patterns due to branching structure
- High turbulent kinetic energy in coral region
- Significant wave height reduction downstream
- Multiple velocity acceleration zones
- Strong pressure variations across coral surface
"""

    instructions_file = os.path.join(case_dir, 'paraview_instructions.txt')
    with open(instructions_file, 'w') as f:
        f.write(instructions)
    
    return instructions_file

def main():
    if len(sys.argv) < 2:
        case_dir = "cases/BR01_wave_case"  # Default case
    else:
        case_dir = sys.argv[1]
    
    if not os.path.exists(case_dir):
        print(f"Case directory {case_dir} not found")
        return
    
    print(f"Analyzing contour data for: {os.path.basename(case_dir)}")
    print()
    
    # Create field summary
    create_simple_field_summary(case_dir)
    
    # Create ParaView instructions
    instructions_file = create_paraview_instructions(case_dir)
    
    print(f"\n" + "="*60)
    print("VISUALIZATION READY!")
    print("="*60)
    print(f"Detailed instructions saved to: {instructions_file}")
    print()
    print("QUICK START:")
    print(f"  paraFoam -case {case_dir}")
    print()
    print("This will open ParaView with your simulation data loaded.")
    print("Follow the instructions file for creating longitudinal cross-sections.")

if __name__ == "__main__":
    main()