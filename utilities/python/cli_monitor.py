#!/usr/bin/env python3
"""
Command-line OpenFOAM Simulation Monitor
A simple text-based monitoring tool for OpenFOAM wave energy simulations
"""

import subprocess
import sys
import os
import time
import re
from datetime import datetime

class OpenFOAMCLIMonitor:
    def __init__(self):
        self.cases = [
            'BR01_wave_case',
            'CY02_wave_case', 
            'EN03_wave_case',
            'MA04_wave_case',
            'TB05_wave_case'
        ]
        
    def print_header(self):
        print("=" * 60)
        print("    OpenFOAM Wave Energy Simulation Monitor")
        print("=" * 60)
        
    def select_case(self):
        print("\nAvailable Cases:")
        for i, case in enumerate(self.cases, 1):
            coral_type = {
                'BR01': 'Branching',
                'CY02': 'Corymbose', 
                'EN03': 'Encrusting',
                'MA04': 'Massive',
                'TB05': 'Table'
            }
            case_id = case[:4]
            print(f"  {i}. {case} ({coral_type.get(case_id, 'Unknown')})")
            
        while True:
            try:
                choice = input(f"\nSelect case (1-{len(self.cases)}): ")
                case_index = int(choice) - 1
                if 0 <= case_index < len(self.cases):
                    return self.cases[case_index]
                else:
                    print("Invalid selection!")
            except ValueError:
                print("Please enter a number!")
                
    def check_case_status(self, case_name):
        case_path = f"cases/{case_name}"
        if not os.path.exists(case_path):
            print(f"❌ Case directory not found: {case_path}")
            return False
            
        print(f"\n📁 Case Status: {case_name}")
        print("-" * 40)
        
        # Check directories
        dirs_to_check = ['0', 'constant', 'system']
        for dir_name in dirs_to_check:
            dir_path = os.path.join(case_path, dir_name)
            status = "✅" if os.path.exists(dir_path) else "❌"
            print(f"  {status} {dir_name}/")
            
        # Check key files
        files_to_check = [
            'system/controlDict',
            'system/fvSolution', 
            'system/fvSchemes',
            'constant/transportProperties'
        ]
        
        for file_path in files_to_check:
            full_path = os.path.join(case_path, file_path)
            status = "✅" if os.path.exists(full_path) else "❌"
            print(f"  {status} {file_path}")
            
        # Check mesh
        mesh_path = os.path.join(case_path, 'constant/polyMesh')
        if os.path.exists(mesh_path):
            print(f"  ✅ Mesh generated (polyMesh/)")
        else:
            print(f"  ❌ No mesh found - run blockMesh first")
            
        return True
        
    def run_simulation_step(self, case_name, step):
        case_path = f"cases/{case_name}"
        
        steps = {
            '1': ('cleanup', 'Clean Previous Data'),
            '2': ('blockmesh', 'Generate Base Mesh'),
            '3': ('copy_stl', 'Copy Coral Geometry'),  
            '4': ('setfields', 'Initialize Fields'),
            '5': ('snappyhex', 'Generate Coral Mesh'),
            '6': ('checkmesh', 'Check Mesh Quality'),
            '7': ('interfoam', 'Run Simulation')
        }
        
        if step not in steps:
            print("Invalid step!")
            return False
            
        step_id, step_name = steps[step]
        print(f"\n🚀 Starting: {step_name}")
        print("-" * 40)
        
        try:
            if step_id == 'cleanup':
                self._cleanup_case(case_path)
            elif step_id == 'blockmesh':
                self._run_openfoam_command(case_path, "blockMesh")
            elif step_id == 'copy_stl':
                self._copy_stl_geometry(case_name, case_path)
            elif step_id == 'setfields':
                self._run_openfoam_command(case_path, "setFields")
            elif step_id == 'snappyhex':
                self._run_openfoam_command(case_path, "snappyHexMesh -overwrite")
            elif step_id == 'checkmesh':
                self._run_openfoam_command(case_path, "checkMesh")
            elif step_id == 'interfoam':
                self._run_openfoam_command_with_monitoring(case_path, "interFoam")
                
            print(f"✅ Completed: {step_name}")
            return True
            
        except Exception as e:
            print(f"❌ Error in {step_name}: {str(e)}")
            return False
            
    def _cleanup_case(self, case_path):
        print("Cleaning previous simulation data...")
        cleanup_patterns = ['processor*', '0.*', '[1-9]*', 'postProcessing', 'constant/polyMesh', 'log.*']
        
        for pattern in cleanup_patterns:
            cmd = f"cd {case_path} && rm -rf {pattern} 2>/dev/null || true"
            subprocess.run(cmd, shell=True)
            
        print("✅ Cleanup completed")
        
    def _copy_stl_geometry(self, case_name, case_path):
        case_id = case_name[:4]
        stl_patterns = {
            'BR01': 'BR01_branching.stl',
            'CY02': 'CY02_corymbose.stl', 
            'EN03': 'EN03_encrusting.stl',
            'MA04': 'MA04_massive.stl',
            'TB05': 'TB05_table.stl'
        }
        
        stl_file = stl_patterns.get(case_id)
        if not stl_file:
            raise Exception(f"Unknown case ID: {case_id}")
            
        src_path = f"{case_path}/geom/{stl_file}"
        if not os.path.exists(src_path):
            raise Exception(f"STL file not found: {src_path}")
            
        cmd = f"mkdir -p {case_path}/constant/triSurface && cp {src_path} {case_path}/constant/triSurface/coral.stl"
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        if result.returncode != 0:
            raise Exception(f"Failed to copy STL: {result.stderr}")
            
        print(f"✅ Copied {stl_file} to triSurface/")
        
    def _run_openfoam_command(self, case_path, command):
        print(f"Running: {command}")
        full_command = f"cd {case_path} && ../../openfoam.sh -- {command}"
        
        result = subprocess.run(full_command, shell=True, capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"Command output:\n{result.stdout}")
            print(f"Command errors:\n{result.stderr}")
            raise Exception(f"Command failed with return code {result.returncode}")
            
        print(f"✅ {command} completed successfully")
        
    def _run_openfoam_command_with_monitoring(self, case_path, command):
        print(f"Running: {command} (with live monitoring)")
        full_command = f"cd {case_path} && ../../openfoam.sh -- {command}"
        
        process = subprocess.Popen(
            full_command,
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        print("\n📊 Live Output:")
        print("-" * 40)
        
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                # Print with timestamp
                timestamp = datetime.now().strftime("%H:%M:%S")
                print(f"[{timestamp}] {output.strip()}")
                
                # Parse for key information
                if 'Time =' in output:
                    match = re.search(r'Time = ([\d.]+)', output)
                    if match:
                        current_time = float(match.group(1))
                        print(f"  ⏱️  Simulation time: {current_time:.3f}s")
                        
                elif 'Solving for' in output and 'Initial residual' in output:
                    print(f"  📈 {output.strip()}")
                    
        rc = process.poll()
        if rc != 0:
            raise Exception(f"Command failed with return code {rc}")
            
        print(f"\n✅ {command} completed successfully")
        
    def show_menu(self, case_name):
        while True:
            print(f"\n🎛️  Simulation Control - {case_name}")
            print("=" * 50)
            print("1. Clean Previous Data")
            print("2. Generate Base Mesh (blockMesh)")
            print("3. Copy Coral Geometry")
            print("4. Initialize Fields (setFields)")
            print("5. Generate Coral Mesh (snappyHexMesh)")
            print("6. Check Mesh Quality")
            print("7. Run Simulation (interFoam)")
            print("8. Run All Steps")
            print("9. Check Case Status")
            print("0. Exit")
            
            choice = input("\nSelect option: ")
            
            if choice == '0':
                print("Goodbye!")
                break
            elif choice == '8':
                print("\n🚀 Running All Steps...")
                for step in ['1', '2', '3', '4', '5', '6', '7']:
                    if not self.run_simulation_step(case_name, step):
                        print("❌ Stopping due to error")
                        break
                print("\n🎉 All steps completed!")
            elif choice == '9':
                self.check_case_status(case_name)
            elif choice in ['1', '2', '3', '4', '5', '6', '7']:
                self.run_simulation_step(case_name, choice)
            else:
                print("Invalid choice!")
                
    def run(self):
        self.print_header()
        
        # Check if we're in the right directory
        if not os.path.exists('openfoam.sh'):
            print("❌ Error: openfoam.sh not found!")
            print("Please run this script from the wave_energy_study directory")
            return
            
        case_name = self.select_case()
        print(f"\n✅ Selected case: {case_name}")
        
        self.check_case_status(case_name)
        self.show_menu(case_name)

def main():
    monitor = OpenFOAMCLIMonitor()
    monitor.run()

if __name__ == "__main__":
    main()