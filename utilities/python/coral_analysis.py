#!/usr/bin/env python3
"""
Unified Coral Analysis Utilities

This module provides common functionality for analyzing coral geometries,
simulation results, and wave energy data across all coral morphologies.
"""

import struct
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import os
import glob
from typing import Dict, List, Optional, Tuple, Any

class CoralGeometryAnalyzer:
    """Analyzer for coral STL geometry files"""
    
    def __init__(self, coral_id: str):
        self.coral_id = coral_id
        self.coral_names = {
            'BR01': 'Branching',
            'CY02': 'Corymbose', 
            'EN03': 'Encrusting',
            'MA04': 'Massive',
            'TB05': 'Table'
        }
    
    def read_stl_binary(self, filename: str) -> Tuple[np.ndarray, List, int]:
        """Read binary STL file and return triangles"""
        with open(filename, 'rb') as f:
            header = f.read(80)
            num_triangles = struct.unpack('<I', f.read(4))[0]
            
            triangles = []
            vertices = []
            
            for i in range(num_triangles):
                normal = struct.unpack('<3f', f.read(12))
                v1 = struct.unpack('<3f', f.read(12))
                v2 = struct.unpack('<3f', f.read(12))
                v3 = struct.unpack('<3f', f.read(12))
                f.read(2)  # Skip attribute byte count
                
                vertices.extend([v1, v2, v3])
                triangles.append([normal, v1, v2, v3])
            
            return np.array(vertices), triangles, num_triangles
    
    def analyze_geometry(self, vertices: np.ndarray) -> Dict[str, Any]:
        """Analyze STL geometry bounds and dimensions"""
        vertices = np.array(vertices)
        
        min_coords = np.min(vertices, axis=0)
        max_coords = np.max(vertices, axis=0)
        center = (min_coords + max_coords) / 2
        dimensions = max_coords - min_coords
        
        return {
            'min_coords': min_coords,
            'max_coords': max_coords,
            'center': center,
            'dimensions': dimensions,
            'volume_envelope': dimensions[0] * dimensions[1] * dimensions[2]
        }
    
    def analyze_coral_file(self, stl_file: str) -> Dict[str, Any]:
        """Complete analysis of a coral STL file"""
        try:
            vertices, triangles, num_triangles = self.read_stl_binary(stl_file)
            geometry = self.analyze_geometry(vertices)
            
            return {
                'coral_id': self.coral_id,
                'coral_name': self.coral_names.get(self.coral_id, self.coral_id),
                'file_path': stl_file,
                'num_triangles': num_triangles,
                'geometry': geometry,
                'file_size': os.path.getsize(stl_file),
                'is_valid_scale': 0.04 < geometry['dimensions'][2] < 0.06,
                'success': True
            }
        except Exception as e:
            return {
                'coral_id': self.coral_id,
                'success': False,
                'error': str(e)
            }

class WaveEnergyAnalyzer:
    """Analyzer for wave energy reduction from OpenFOAM results"""
    
    def __init__(self, case_dir: str):
        self.case_dir = case_dir
        self.case_name = os.path.basename(case_dir)
        self.coral_type = self.case_name.split('_')[0]
    
    def load_force_data(self) -> Optional[pd.DataFrame]:
        """Load force coefficient data from OpenFOAM simulation"""
        force_file = os.path.join(self.case_dir, 'postProcessing', 'forceCoeffs', '0', 'forceCoeffs.dat')
        
        if not os.path.exists(force_file):
            return None
        
        try:
            data = pd.read_csv(force_file, sep=r'\s+', comment='#', 
                              names=['time', 'Cd', 'Cs', 'Cl', 'CmRoll', 'CmPitch', 'CmYaw'])
            return data
        except Exception as e:
            print(f"Error reading force data from {self.case_dir}: {e}")
            return None
    
    def load_wave_gauge_data(self) -> Optional[Dict[str, pd.DataFrame]]:
        """Load wave elevation data from wave gauges"""
        gauge_files = glob.glob(os.path.join(self.case_dir, 'postProcessing', 'waveGauges', '*', '*.raw'))
        
        if not gauge_files:
            return None
        
        gauge_data = {}
        for file_path in gauge_files:
            gauge_name = os.path.basename(os.path.dirname(file_path))
            try:
                data = pd.read_csv(file_path, sep=r'\s+', comment='#')
                gauge_data[gauge_name] = data
            except Exception as e:
                print(f"Error reading gauge data from {file_path}: {e}")
        
        return gauge_data if gauge_data else None
    
    def calculate_wave_energy_reduction(self, upstream_data: pd.DataFrame, 
                                       downstream_data: pd.DataFrame) -> Optional[float]:
        """Calculate wave energy reduction based on wave height data"""
        if upstream_data is None or downstream_data is None:
            return None
        
        try:
            upstream_alpha = upstream_data.iloc[:, -1]
            downstream_alpha = downstream_data.iloc[:, -1]
            
            upstream_height = upstream_alpha.max() - upstream_alpha.min()
            downstream_height = downstream_alpha.max() - downstream_alpha.min()
            
            upstream_energy = upstream_height ** 2
            downstream_energy = downstream_height ** 2
            
            if upstream_energy > 0:
                energy_reduction = ((upstream_energy - downstream_energy) / upstream_energy) * 100
                return max(0, energy_reduction)
            else:
                return 0
                
        except Exception as e:
            print(f"Error calculating wave energy reduction: {e}")
            return None
    
    def analyze_case(self) -> Dict[str, Any]:
        """Analyze a single coral case"""
        results = {
            'case': self.case_name,
            'coral_type': self.coral_type,
            'avg_drag_coeff': None,
            'max_drag_coeff': None,
            'avg_lift_coeff': None,
            'wave_energy_reduction': None,
            'simulation_success': False
        }
        
        # Load and analyze force data
        force_data = self.load_force_data()
        if force_data is not None and len(force_data) > 0:
            steady_start = len(force_data) // 2
            steady_data = force_data.iloc[steady_start:]
            
            results['avg_drag_coeff'] = steady_data['Cd'].mean()
            results['max_drag_coeff'] = steady_data['Cd'].max()
            results['avg_lift_coeff'] = steady_data['Cl'].mean()
            results['simulation_success'] = True
        
        # Load and analyze wave gauge data
        gauge_data = self.load_wave_gauge_data()
        if gauge_data:
            upstream = gauge_data.get('gaugeUpstream')
            downstream = gauge_data.get('gaugeDownstream')
            
            if upstream is not None and downstream is not None:
                energy_reduction = self.calculate_wave_energy_reduction(upstream, downstream)
                if energy_reduction is not None:
                    results['wave_energy_reduction'] = energy_reduction
        
        return results

class CoralSimulationManager:
    """Manager for coral simulation workflows"""
    
    def __init__(self, base_dir: str = "."):
        self.base_dir = base_dir
        self.coral_cases = ['BR01_wave_case', 'CY02_wave_case', 'EN03_wave_case', 
                           'MA04_wave_case', 'TB05_wave_case']
        self.coral_names = {
            'BR01': 'Branching',
            'CY02': 'Corymbose', 
            'EN03': 'Encrusting',
            'MA04': 'Massive',
            'TB05': 'Table'
        }
    
    def get_case_path(self, case_name: str) -> str:
        """Get full path to a case directory"""
        return os.path.join(self.base_dir, "cases", case_name)
    
    def list_available_cases(self) -> List[str]:
        """List available coral cases"""
        available = []
        for case in self.coral_cases:
            case_path = self.get_case_path(case)
            if os.path.exists(case_path):
                available.append(case)
        return available
    
    def analyze_all_geometries(self) -> List[Dict[str, Any]]:
        """Analyze geometries for all available cases"""
        results = []
        
        for case in self.list_available_cases():
            coral_id = case.split('_')[0]
            case_path = self.get_case_path(case)
            stl_file = os.path.join(case_path, "constant", "triSurface", "coral.stl")
            
            if os.path.exists(stl_file):
                analyzer = CoralGeometryAnalyzer(coral_id)
                result = analyzer.analyze_coral_file(stl_file)
                results.append(result)
        
        return results
    
    def analyze_all_wave_energy(self) -> List[Dict[str, Any]]:
        """Analyze wave energy for all available cases"""
        results = []
        
        for case in self.list_available_cases():
            case_path = self.get_case_path(case)
            analyzer = WaveEnergyAnalyzer(case_path)
            result = analyzer.analyze_case()
            result['coral_name'] = self.coral_names.get(result['coral_type'], result['coral_type'])
            results.append(result)
        
        return results
    
    def generate_summary_report(self) -> str:
        """Generate comprehensive analysis report"""
        report = ["CORAL MORPHOLOGY HYDRODYNAMICS ANALYSIS"]
        report.append("=" * 50)
        report.append("Wave Parameters: H=0.16m, T=1.0s, depth=0.16m")
        report.append("Simulation time: 10 seconds (10 wave periods)")
        report.append("")
        
        # Geometry analysis
        geometry_results = self.analyze_all_geometries()
        if geometry_results:
            report.append("GEOMETRY ANALYSIS")
            report.append("-" * 30)
            for result in geometry_results:
                if result['success']:
                    geom = result['geometry']
                    report.append(f"{result['coral_name']:12}: "
                                f"H={geom['dimensions'][2]:.3f}m, "
                                f"Triangles={result['num_triangles']:,}, "
                                f"Valid={'✅' if result['is_valid_scale'] else '⚠️'}")
            report.append("")
        
        # Wave energy analysis
        wave_results = self.analyze_all_wave_energy()
        successful_cases = [r for r in wave_results if r['simulation_success']]
        
        if successful_cases:
            report.append("WAVE ENERGY REDUCTION RANKING")
            report.append("-" * 30)
            
            # Sort by energy reduction
            energy_sorted = sorted([r for r in successful_cases if r['wave_energy_reduction'] is not None],
                                 key=lambda x: x['wave_energy_reduction'], reverse=True)
            
            for result in energy_sorted:
                report.append(f"{result['coral_name']:12}: {result['wave_energy_reduction']:5.1f}% energy reduction")
            
            report.append("")
            report.append("DRAG COEFFICIENT ANALYSIS")
            report.append("-" * 30)
            for result in successful_cases:
                if result['avg_drag_coeff'] is not None:
                    report.append(f"{result['coral_name']:12}: "
                                f"Cd_avg = {result['avg_drag_coeff']:5.3f}, "
                                f"Cd_max = {result['max_drag_coeff']:5.3f}")
        
        return "\n".join(report)

def main():
    """Main execution function for unified analysis"""
    manager = CoralSimulationManager()
    
    print("Available cases:", manager.list_available_cases())
    print()
    
    report = manager.generate_summary_report()
    print(report)
    
    # Save detailed results
    wave_results = manager.analyze_all_wave_energy()
    df = pd.DataFrame(wave_results)
    df.to_csv('unified_coral_analysis.csv', index=False)
    print(f"\nDetailed results saved to: unified_coral_analysis.csv")

if __name__ == "__main__":
    main()