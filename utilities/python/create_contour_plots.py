#!/usr/bin/env python3
"""
Generate longitudinal cross-sectional contour plots for coral wave simulations

This script creates contour plots showing:
1. Wave field (alpha.water) - water fraction
2. Velocity magnitude (U) - flow field
3. Pressure field (p_rgh) - pressure distribution
4. Turbulent kinetic energy (k) - turbulence intensity

Usage: python create_contour_plots.py <case_directory>
"""

import os
import sys
import subprocess
import numpy as np

def create_paraview_script(case_dir, case_name):
    """Create ParaView Python script for automated plot generation"""
    
    script_content = f'''
# ParaView Python script for longitudinal cross-sectional plots
import paraview.simple as pv

# Load the case
case_file = "{case_dir}/{case_name}.foam"
reader = pv.OpenFOAMReader(FileName=case_file)

# Get the latest time step
reader.UpdatePipeline()
times = reader.TimestepValues
if times:
    latest_time = times[-1]
    reader.SMProxy.SetPropertyWithName("TimestepValues", [latest_time])

# Create a longitudinal slice (Y-Z plane through the center)
slice1 = pv.Slice(Input=reader)
slice1.SliceType = 'Plane'
slice1.SliceType.Origin = [0.2, 0.0, 0.0]  # Middle of domain
slice1.SliceType.Normal = [1.0, 0.0, 0.0]  # X-normal plane

# Set up the render view
renderView = pv.GetActiveViewOrCreate('RenderView')
renderView.ViewSize = [1200, 800]
renderView.CameraPosition = [0.5, 0.0, 0.3]
renderView.CameraFocalPoint = [0.2, 0.0, 0.0]

# Display the slice
sliceDisplay = pv.Show(slice1, renderView)

# Plot 1: Alpha.water (wave field)
pv.ColorBy(sliceDisplay, ('POINTS', 'alpha.water'))
sliceDisplay.RescaleTransferFunctionToDataRange(True, False)
sliceDisplay.SetScalarBarVisibility(renderView, True)

# Get color transfer function
alphaTF = pv.GetColorTransferFunction('alpha.water')
alphaTF.RescaleTransferFunction(0.0, 1.0)

# Set view and save
renderView.CameraViewUp = [0.0, 0.0, 1.0]
pv.WriteImage('{case_dir}/alpha_water_longitudinal.png')

# Plot 2: Velocity magnitude
U_data = reader.PointData.GetArray('U')
if U_data:
    calc = pv.Calculator(Input=slice1)
    calc.ResultArrayName = 'U_magnitude'
    calc.Function = 'mag(U)'
    
    calcDisplay = pv.Show(calc, renderView)
    pv.Hide(slice1, renderView)
    
    pv.ColorBy(calcDisplay, ('POINTS', 'U_magnitude'))
    calcDisplay.RescaleTransferFunctionToDataRange(True, False)
    
    # Get color transfer function for velocity
    velTF = pv.GetColorTransferFunction('U_magnitude')
    velTF.ApplyPreset('Rainbow Desaturated', True)
    
    pv.WriteImage('{case_dir}/velocity_magnitude_longitudinal.png')
    pv.Hide(calc, renderView)

# Plot 3: Pressure field
if reader.PointData.GetArray('p_rgh'):
    pv.Show(slice1, renderView)
    pv.ColorBy(sliceDisplay, ('POINTS', 'p_rgh'))
    sliceDisplay.RescaleTransferFunctionToDataRange(True, False)
    
    pressTF = pv.GetColorTransferFunction('p_rgh')
    pressTF.ApplyPreset('Cool to Warm', True)
    
    pv.WriteImage('{case_dir}/pressure_longitudinal.png')

# Plot 4: Turbulent kinetic energy
if reader.PointData.GetArray('k'):
    pv.ColorBy(sliceDisplay, ('POINTS', 'k'))
    sliceDisplay.RescaleTransferFunctionToDataRange(True, False)
    
    kTF = pv.GetColorTransferFunction('k')
    kTF.ApplyPreset('Viridis (matplotlib)', True)
    
    pv.WriteImage('{case_dir}/turbulent_ke_longitudinal.png')

print("Contour plots generated successfully:")
print("- alpha_water_longitudinal.png (wave field)")
print("- velocity_magnitude_longitudinal.png (flow field)")  
print("- pressure_longitudinal.png (pressure distribution)")
print("- turbulent_ke_longitudinal.png (turbulence intensity)")
'''
    
    script_file = f"{case_dir}/generate_plots.py"
    with open(script_file, 'w') as f:
        f.write(script_content)
    
    return script_file

def run_paraview_batch(script_file):
    """Run ParaView in batch mode"""
    try:
        # Try different ParaView executables
        paraview_executables = [
            'pvpython',
            'paraview',
            '/usr/lib/openfoam/openfoam2212/bin/paraFoam',
            'pvbatch'
        ]
        
        for executable in paraview_executables:
            if subprocess.run(['which', executable], capture_output=True).returncode == 0:
                print(f"Using {executable} for visualization...")
                if executable == 'paraFoam':
                    # For paraFoam, we need a different approach
                    print("paraFoam detected - opening case for manual visualization")
                    print(f"Run: paraFoam -case {os.path.dirname(script_file)}")
                    return False
                else:
                    result = subprocess.run([executable, script_file], 
                                          capture_output=True, text=True)
                    if result.returncode == 0:
                        return True
                    else:
                        print(f"Error with {executable}: {result.stderr}")
        
        return False
        
    except Exception as e:
        print(f"Error running ParaView: {e}")
        return False

def create_simple_contour_info(case_dir):
    """Create information about available contour data"""
    
    info_content = f"""
LONGITUDINAL CROSS-SECTIONAL CONTOUR ANALYSIS
=============================================

Case: {os.path.basename(case_dir)}

AVAILABLE FIELD DATA FOR VISUALIZATION:
"""
    
    # Check latest time directory
    time_dirs = [d for d in os.listdir(case_dir) if d.replace('.', '').isdigit()]
    if time_dirs:
        latest_time = sorted(time_dirs, key=float)[-1]
        time_path = os.path.join(case_dir, latest_time)
        
        info_content += f"\nLatest time step: {latest_time} s\n"
        info_content += "Available fields:\n"
        
        for field_file in os.listdir(time_path):
            if os.path.isfile(os.path.join(time_path, field_file)):
                field_descriptions = {
                    'alpha.water': 'Water volume fraction (0=air, 1=water)',
                    'U': 'Velocity vector field [m/s]',
                    'p_rgh': 'Hydrostatic pressure [Pa]',
                    'k': 'Turbulent kinetic energy [m²/s²]',
                    'omega': 'Specific dissipation rate [1/s]',
                    'nut': 'Turbulent viscosity [m²/s]'
                }
                
                description = field_descriptions.get(field_file, 'OpenFOAM field data')
                info_content += f"  - {field_file}: {description}\n"
    
    info_content += f"""

VISUALIZATION OPTIONS:
=====================

1. ParaView GUI (Recommended):
   paraFoam -case {case_dir}
   
   Steps for longitudinal cross-section:
   a) Load case and go to latest time step
   b) Create slice filter (Filters → Alphabetical → Slice)
   c) Set slice normal to [1,0,0] (X-direction)
   d) Position slice at x=0.2 (domain center)
   e) Color by desired field (alpha.water, U magnitude, p_rgh, k)

2. Automated Python visualization:
   pvpython {case_dir}/generate_plots.py

3. Manual slice extraction:
   sample -case {case_dir} -time {latest_time if 'latest_time' in locals() else '0.2'}

EXPECTED CONTOUR FEATURES:
=========================
- Wave propagation pattern in alpha.water field
- Velocity amplification/reduction around coral
- Pressure variations due to flow obstruction  
- Turbulent kinetic energy peaks in coral wake
- Energy dissipation zones downstream of coral

ANALYSIS INTERPRETATION:
=======================
- High alpha.water values (blue) = water regions
- Low alpha.water values (red) = air/wave troughs
- Velocity magnitude shows flow acceleration/deceleration
- Pressure contours indicate wave loading on coral
- Turbulent k.e. quantifies energy dissipation effectiveness
"""
    
    info_file = os.path.join(case_dir, 'contour_visualization_guide.txt')
    with open(info_file, 'w') as f:
        f.write(info_content)
    
    return info_file

def main():
    if len(sys.argv) < 2:
        print("Usage: python create_contour_plots.py <case_directory>")
        print("Example: python create_contour_plots.py cases/BR01_wave_case")
        sys.exit(1)
    
    case_dir = sys.argv[1]
    if not os.path.exists(case_dir):
        print(f"Error: Case directory {case_dir} not found")
        sys.exit(1)
    
    case_name = os.path.basename(case_dir)
    
    print(f"Creating contour visualization setup for {case_name}...")
    
    # Create ParaView script
    script_file = create_paraview_script(case_dir, case_name)
    print(f"ParaView script created: {script_file}")
    
    # Create visualization guide
    info_file = create_simple_contour_info(case_dir)
    print(f"Visualization guide created: {info_file}")
    
    # Try to run ParaView batch processing
    print("\\nAttempting automated visualization...")
    if run_paraview_batch(script_file):
        print("Contour plots generated successfully!")
    else:
        print("Automated visualization failed.")
        print("Use manual ParaView visualization:")
        print(f"  paraFoam -case {case_dir}")
        print(f"Or check the guide: {info_file}")

if __name__ == "__main__":
    main()