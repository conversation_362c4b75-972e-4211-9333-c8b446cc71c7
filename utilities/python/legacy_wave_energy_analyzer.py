#!/usr/bin/env python3
"""
Wave Energy Reduction Analysis Script for Coral Simulations

This script analyzes OpenFOAM simulation results to calculate:
1. Wave energy reduction percentage
2. Drag and inertia coefficients
3. Transmission coefficients
4. Comparative analysis between coral types

Usage: python3 analyze_wave_energy.py [case_directory]
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import os
import sys
import glob

def load_force_data(case_dir):
    """Load force coefficient data from OpenFOAM simulation"""
    force_file = os.path.join(case_dir, 'postProcessing', 'forceCoeffs', '0', 'forceCoeffs.dat')
    
    if not os.path.exists(force_file):
        print(f"Warning: Force data not found in {case_dir}")
        return None
    
    try:
        # Read OpenFOAM force coefficient data
        data = pd.read_csv(force_file, sep='\s+', comment='#', 
                          names=['time', 'Cd', 'Cs', 'Cl', 'CmRoll', 'CmPitch', 'CmYaw'])
        return data
    except Exception as e:
        print(f"Error reading force data from {case_dir}: {e}")
        return None

def load_wave_gauge_data(case_dir):
    """Load wave elevation data from wave gauges"""
    gauge_files = glob.glob(os.path.join(case_dir, 'postProcessing', 'waveGauges', '*', '*.raw'))
    
    if not gauge_files:
        print(f"Warning: Wave gauge data not found in {case_dir}")
        return None
    
    gauge_data = {}
    for file_path in gauge_files:
        gauge_name = os.path.basename(os.path.dirname(file_path))
        try:
            data = pd.read_csv(file_path, sep='\s+', comment='#')
            gauge_data[gauge_name] = data
        except Exception as e:
            print(f"Error reading gauge data from {file_path}: {e}")
    
    return gauge_data if gauge_data else None

def calculate_wave_energy_reduction(upstream_data, downstream_data):
    """Calculate wave energy reduction based on wave height data"""
    if upstream_data is None or downstream_data is None:
        return None
    
    # Assuming wave height is proportional to alpha.water variation
    # This is a simplified approach - proper wave energy requires integration
    
    try:
        # Get alpha.water columns (assuming last column contains the data)
        upstream_alpha = upstream_data.iloc[:, -1]
        downstream_alpha = downstream_data.iloc[:, -1]
        
        # Calculate wave heights (simplified as alpha variation range)
        upstream_height = upstream_alpha.max() - upstream_alpha.min()
        downstream_height = downstream_alpha.max() - downstream_alpha.min()
        
        # Wave energy is proportional to H²
        upstream_energy = upstream_height ** 2
        downstream_energy = downstream_height ** 2
        
        # Energy reduction percentage
        if upstream_energy > 0:
            energy_reduction = ((upstream_energy - downstream_energy) / upstream_energy) * 100
            return max(0, energy_reduction)  # Ensure non-negative
        else:
            return 0
            
    except Exception as e:
        print(f"Error calculating wave energy reduction: {e}")
        return None

def analyze_single_case(case_dir):
    """Analyze a single coral case"""
    case_name = os.path.basename(case_dir)
    coral_type = case_name.split('_')[0]
    
    print(f"\nAnalyzing case: {case_name}")
    print("-" * 40)
    
    results = {
        'case': case_name,
        'coral_type': coral_type,
        'avg_drag_coeff': None,
        'max_drag_coeff': None,
        'avg_lift_coeff': None,
        'wave_energy_reduction': None,
        'simulation_success': False
    }
    
    # Load and analyze force data
    force_data = load_force_data(case_dir)
    if force_data is not None and len(force_data) > 0:
        # Analyze steady-state period (last 50% of simulation)
        steady_start = len(force_data) // 2
        steady_data = force_data.iloc[steady_start:]
        
        results['avg_drag_coeff'] = steady_data['Cd'].mean()
        results['max_drag_coeff'] = steady_data['Cd'].max()
        results['avg_lift_coeff'] = steady_data['Cl'].mean()
        
        print(f"Average drag coefficient: {results['avg_drag_coeff']:.3f}")
        print(f"Maximum drag coefficient: {results['max_drag_coeff']:.3f}")
        print(f"Average lift coefficient: {results['avg_lift_coeff']:.3f}")
        
        results['simulation_success'] = True
    
    # Load and analyze wave gauge data
    gauge_data = load_wave_gauge_data(case_dir)
    if gauge_data:
        # Look for upstream and downstream gauges
        upstream = gauge_data.get('gaugeUpstream')
        downstream = gauge_data.get('gaugeDownstream')
        
        if upstream is not None and downstream is not None:
            energy_reduction = calculate_wave_energy_reduction(upstream, downstream)
            if energy_reduction is not None:
                results['wave_energy_reduction'] = energy_reduction
                print(f"Wave energy reduction: {energy_reduction:.1f}%")
    
    return results

def analyze_all_cases():
    """Analyze all coral cases and generate comparative report"""
    cases = ['BR01_wave_case', 'CY02_wave_case', 'EN03_wave_case', 'MA04_wave_case', 'TB05_wave_case']
    coral_names = {
        'BR01': 'Branching',
        'CY02': 'Corymbose', 
        'EN03': 'Encrusting',
        'MA04': 'Massive',
        'TB05': 'Table'
    }
    
    all_results = []
    
    print("CORAL WAVE ENERGY REDUCTION ANALYSIS")
    print("=" * 50)
    print("Wave Parameters: H=0.16m, T=1.0s, depth=0.16m")
    print("Simulation time: 10 seconds (10 wave periods)")
    print()
    
    for case in cases:
        if os.path.exists(case):
            results = analyze_single_case(case)
            results['coral_name'] = coral_names.get(results['coral_type'], results['coral_type'])
            all_results.append(results)
    
    if not all_results:
        print("No simulation results found. Run simulations first.")
        return
    
    # Create summary DataFrame
    df = pd.DataFrame(all_results)
    
    # Generate summary report
    print("\nSUMMARY REPORT")
    print("=" * 50)
    
    successful_cases = df[df['simulation_success'] == True]
    
    if len(successful_cases) > 0:
        print(f"Successful simulations: {len(successful_cases)}/{len(all_results)}")
        print()
        
        # Sort by wave energy reduction
        if successful_cases['wave_energy_reduction'].notna().any():
            sorted_df = successful_cases.sort_values('wave_energy_reduction', ascending=False)
            print("WAVE ENERGY REDUCTION RANKING:")
            for idx, row in sorted_df.iterrows():
                if pd.notna(row['wave_energy_reduction']):
                    print(f"{row['coral_name']:12}: {row['wave_energy_reduction']:5.1f}% energy reduction")
        
        print("\nDRAG COEFFICIENT ANALYSIS:")
        for idx, row in successful_cases.iterrows():
            if pd.notna(row['avg_drag_coeff']):
                print(f"{row['coral_name']:12}: Cd_avg = {row['avg_drag_coeff']:5.3f}, Cd_max = {row['max_drag_coeff']:5.3f}")
        
        # Save results to CSV
        output_file = 'wave_energy_analysis_results.csv'
        df.to_csv(output_file, index=False)
        print(f"\nDetailed results saved to: {output_file}")
        
        # Generate plots if matplotlib available
        try:
            generate_plots(successful_cases)
        except Exception as e:
            print(f"Could not generate plots: {e}")
    
    else:
        print("No successful simulations found.")

def generate_plots(df):
    """Generate analysis plots"""
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 10))
    
    # Plot 1: Wave Energy Reduction
    valid_energy = df.dropna(subset=['wave_energy_reduction'])
    if len(valid_energy) > 0:
        ax1.bar(valid_energy['coral_name'], valid_energy['wave_energy_reduction'])
        ax1.set_title('Wave Energy Reduction by Coral Type')
        ax1.set_ylabel('Energy Reduction (%)')
        ax1.tick_params(axis='x', rotation=45)
    
    # Plot 2: Drag Coefficients
    valid_drag = df.dropna(subset=['avg_drag_coeff'])
    if len(valid_drag) > 0:
        ax2.bar(valid_drag['coral_name'], valid_drag['avg_drag_coeff'])
        ax2.set_title('Average Drag Coefficient')
        ax2.set_ylabel('Cd')
        ax2.tick_params(axis='x', rotation=45)
    
    # Plot 3: Energy vs Drag correlation
    both_valid = df.dropna(subset=['wave_energy_reduction', 'avg_drag_coeff'])
    if len(both_valid) > 0:
        ax3.scatter(both_valid['avg_drag_coeff'], both_valid['wave_energy_reduction'])
        for idx, row in both_valid.iterrows():
            ax3.annotate(row['coral_name'], 
                        (row['avg_drag_coeff'], row['wave_energy_reduction']))
        ax3.set_xlabel('Average Drag Coefficient')
        ax3.set_ylabel('Wave Energy Reduction (%)')
        ax3.set_title('Energy Reduction vs Drag Coefficient')
    
    # Plot 4: Summary comparison
    if len(df) > 0:
        coral_types = df['coral_name'].tolist()
        success_rates = [1 if x else 0 for x in df['simulation_success']]
        ax4.bar(coral_types, success_rates)
        ax4.set_title('Simulation Success Rate')
        ax4.set_ylabel('Success (1=Yes, 0=No)')
        ax4.tick_params(axis='x', rotation=45)
    
    plt.tight_layout()
    plt.savefig('coral_wave_analysis.png', dpi=300, bbox_inches='tight')
    print("Analysis plots saved to: coral_wave_analysis.png")

def main():
    """Main execution function"""
    if len(sys.argv) > 1:
        # Analyze single case
        case_dir = sys.argv[1]
        if os.path.exists(case_dir):
            analyze_single_case(case_dir)
        else:
            print(f"Case directory not found: {case_dir}")
    else:
        # Analyze all cases
        analyze_all_cases()

if __name__ == "__main__":
    main()