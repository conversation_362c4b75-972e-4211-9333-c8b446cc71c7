#!/usr/bin/env python3
"""
OpenFOAM Simulation Monitor GUI
A comprehensive monitoring tool for OpenFOAM wave energy simulations
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, filedialog, messagebox
import subprocess
import threading
import time
import os
import re
import json
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import numpy as np
from datetime import datetime
import queue

class OpenFOAMMonitor:
    def __init__(self, root):
        self.root = root
        self.root.title("OpenFOAM Wave Energy Simulation Monitor")
        self.root.geometry("1400x900")
        
        # Available cases (define first)
        self.cases = [
            'BR01_wave_case',
            'CY02_wave_case', 
            'EN03_wave_case',
            'MA04_wave_case',
            'TB05_wave_case'
        ]
        
        self.setup_ui()
        self.update_log_display()
        
        # Initialize current case after UI setup
        self.current_case = self.case_var.get()
        self.log_message(f"Initialized with case: {self.current_case}")
        
    def setup_ui(self):
        """Setup the main UI components"""
        
        # Main notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Tab 1: Control Panel
        self.control_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.control_frame, text="Control Panel")
        self.setup_control_panel()
        
        # Tab 2: Real-time Monitoring
        self.monitor_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.monitor_frame, text="Real-time Monitor")
        self.setup_monitor_panel()
        
        # Tab 3: Residuals Plot
        self.residuals_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.residuals_frame, text="Residuals")
        self.setup_residuals_panel()
        
        # Tab 4: Results Analysis
        self.results_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.results_frame, text="Results")
        self.setup_results_panel()
        
    def setup_control_panel(self):
        """Setup the control panel tab"""
        
        # Case selection
        case_frame = ttk.LabelFrame(self.control_frame, text="Case Selection", padding=10)
        case_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(case_frame, text="Select Case:").pack(side=tk.LEFT)
        self.case_var = tk.StringVar(value=self.cases[0])
        case_combo = ttk.Combobox(case_frame, textvariable=self.case_var, values=self.cases, state="readonly")
        case_combo.pack(side=tk.LEFT, padx=10)
        case_combo.bind('<<ComboboxSelected>>', self.on_case_selected)
        
        # Simulation steps
        steps_frame = ttk.LabelFrame(self.control_frame, text="Simulation Steps", padding=10)
        steps_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Step buttons with status indicators
        self.step_buttons = {}
        self.step_status = {}
        
        steps = [
            ('cleanup', 'Clean Previous Data'),
            ('blockmesh', 'Generate Base Mesh'),
            ('copy_stl', 'Copy Coral Geometry'),
            ('setfields', 'Initialize Fields'),
            ('snappyhex', 'Generate Coral Mesh'),
            ('checkmesh', 'Check Mesh Quality'),
            ('interfoam', 'Run Simulation')
        ]
        
        for i, (step_id, step_name) in enumerate(steps):
            row = i // 2
            col = i % 2
            
            step_frame = ttk.Frame(steps_frame)
            step_frame.grid(row=row, column=col, padx=5, pady=3, sticky='ew')
            
            # Status indicator
            status_label = ttk.Label(step_frame, text="●", foreground="gray")
            status_label.pack(side=tk.LEFT)
            self.step_status[step_id] = status_label
            
            # Button
            btn = ttk.Button(step_frame, text=step_name, 
                           command=lambda s=step_id: self.run_step(s))
            btn.pack(side=tk.LEFT, padx=5)
            self.step_buttons[step_id] = btn
        
        # Configure grid weights
        steps_frame.columnconfigure(0, weight=1)
        steps_frame.columnconfigure(1, weight=1)
        
        # Control buttons
        control_buttons_frame = ttk.Frame(self.control_frame)
        control_buttons_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(control_buttons_frame, text="Run All Steps", 
                  command=self.run_all_steps).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_buttons_frame, text="Stop Simulation", 
                  command=self.stop_simulation).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_buttons_frame, text="View Case Info", 
                  command=self.view_case_info).pack(side=tk.LEFT, padx=5)
        
        # Status bar
        self.status_var = tk.StringVar(value="Ready")
        status_bar = ttk.Label(self.control_frame, textvariable=self.status_var, 
                              relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(fill=tk.X, padx=5, pady=2)
        
    def setup_monitor_panel(self):
        """Setup the real-time monitoring panel"""
        
        # Progress frame
        progress_frame = ttk.LabelFrame(self.monitor_frame, text="Simulation Progress", padding=10)
        progress_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, 
                                          maximum=100, length=400)
        self.progress_bar.pack(pady=5)
        
        self.progress_label = ttk.Label(progress_frame, text="Waiting to start...")
        self.progress_label.pack()
        
        # Log display
        log_frame = ttk.LabelFrame(self.monitor_frame, text="Simulation Log", padding=5)
        log_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=20, wrap=tk.WORD)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
        # Log control buttons
        log_control_frame = ttk.Frame(log_frame)
        log_control_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(log_control_frame, text="Clear Log", 
                  command=self.clear_log).pack(side=tk.LEFT, padx=5)
        ttk.Button(log_control_frame, text="Save Log", 
                  command=self.save_log).pack(side=tk.LEFT, padx=5)
        ttk.Button(log_control_frame, text="Auto-scroll", 
                  command=self.toggle_autoscroll).pack(side=tk.LEFT, padx=5)
        
        self.autoscroll = True
        
    def setup_residuals_panel(self):
        """Setup the residuals plotting panel"""
        
        # Matplotlib figure
        self.residuals_fig = Figure(figsize=(12, 8), dpi=100)
        self.residuals_canvas = FigureCanvasTkAgg(self.residuals_fig, self.residuals_frame)
        self.residuals_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # Control frame for residuals
        residuals_control = ttk.Frame(self.residuals_frame)
        residuals_control.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(residuals_control, text="Update Plot", 
                  command=self.update_residuals_plot).pack(side=tk.LEFT, padx=5)
        ttk.Button(residuals_control, text="Save Plot", 
                  command=self.save_residuals_plot).pack(side=tk.LEFT, padx=5)
        
        self.plot_residuals()
        
    def setup_results_panel(self):
        """Setup the results analysis panel"""
        
        # Results summary
        summary_frame = ttk.LabelFrame(self.results_frame, text="Simulation Summary", padding=10)
        summary_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.results_text = scrolledtext.ScrolledText(summary_frame, height=10, wrap=tk.WORD)
        self.results_text.pack(fill=tk.BOTH, expand=True)
        
        # Results actions
        actions_frame = ttk.Frame(self.results_frame)
        actions_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(actions_frame, text="Analyze Forces", 
                  command=self.analyze_forces).pack(side=tk.LEFT, padx=5)
        ttk.Button(actions_frame, text="Generate Report", 
                  command=self.generate_report).pack(side=tk.LEFT, padx=5)
        ttk.Button(actions_frame, text="Open ParaView", 
                  command=self.open_paraview).pack(side=tk.LEFT, padx=5)
        
    def on_case_selected(self, event=None):
        """Handle case selection"""
        self.current_case = self.case_var.get()
        self.log_message(f"Selected case: {self.current_case}")
        self.reset_step_status()
        
    def reset_step_status(self):
        """Reset all step status indicators"""
        for status_label in self.step_status.values():
            status_label.configure(foreground="gray")
            
    def run_step(self, step_id):
        """Run a specific simulation step"""
        if self.is_running:
            messagebox.showwarning("Warning", "Simulation already running!")
            return
            
        # Ensure we have a valid case selected
        if not self.current_case:
            self.current_case = self.case_var.get()
            
        if not self.current_case or self.current_case == "":
            messagebox.showerror("Error", "No case selected! Please select a case first.")
            return
            
        self.log_message(f"Starting step: {step_id}")
        self.step_status[step_id].configure(foreground="orange")
        
        # Run step in background thread
        thread = threading.Thread(target=self._execute_step, args=(step_id,))
        thread.daemon = True
        thread.start()
        
    def _execute_step(self, step_id):
        """Execute a simulation step"""
        try:
            self.is_running = True
            
            # Validate current case
            if not self.current_case:
                raise Exception("No case selected")
                
            case_path = f"cases/{self.current_case}"
            
            # Check if case directory exists
            if not os.path.exists(case_path):
                raise Exception(f"Case directory not found: {case_path}")
            
            if step_id == 'cleanup':
                self._cleanup_case(case_path)
            elif step_id == 'blockmesh':
                self._run_openfoam_command(case_path, "blockMesh")
            elif step_id == 'copy_stl':
                self._copy_stl_geometry(case_path)
            elif step_id == 'setfields':
                self._run_openfoam_command(case_path, "setFields")
            elif step_id == 'snappyhex':
                self._run_openfoam_command(case_path, "snappyHexMesh -overwrite")
            elif step_id == 'checkmesh':
                self._run_openfoam_command(case_path, "checkMesh")
            elif step_id == 'interfoam':
                self._run_openfoam_command(case_path, "interFoam")
                
            self.step_status[step_id].configure(foreground="green")
            self.log_message(f"Completed step: {step_id}")
            
        except Exception as e:
            self.step_status[step_id].configure(foreground="red")
            self.log_message(f"Error in step {step_id}: {str(e)}")
        finally:
            self.is_running = False
            
    def _cleanup_case(self, case_path):
        """Clean previous simulation data"""
        cleanup_dirs = ['processor*', '0.*', '[1-9]*', 'postProcessing', 'constant/polyMesh']
        cleanup_files = ['log.*']
        
        for pattern in cleanup_dirs + cleanup_files:
            cmd = f"cd {case_path} && rm -rf {pattern}"
            subprocess.run(cmd, shell=True)
            
        self.log_message("Cleaned previous simulation data")
        
    def _copy_stl_geometry(self, case_path):
        """Copy STL geometry to constant/triSurface"""
        case_id = self.current_case[:4]
        stl_patterns = {
            'BR01': 'BR01_branching.stl',
            'CY02': 'CY02_corymbose.stl', 
            'EN03': 'EN03_encrusting.stl',
            'MA04': 'MA04_massive.stl',
            'TB05': 'TB05_table.stl'
        }
        
        stl_file = stl_patterns.get(case_id)
        if stl_file:
            cmd = f"mkdir -p {case_path}/constant/triSurface && cp {case_path}/geom/{stl_file} {case_path}/constant/triSurface/coral.stl"
            subprocess.run(cmd, shell=True)
            self.log_message(f"Copied {stl_file} to triSurface")
        else:
            raise Exception(f"Unknown case ID: {case_id}")
            
    def _run_openfoam_command(self, case_path, command):
        """Run OpenFOAM command and capture output"""
        full_command = f"cd {case_path} && ../../openfoam.sh -- {command}"
        
        self.log_message(f"Running: {command}")
        
        process = subprocess.Popen(
            full_command,
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        self.simulation_process = process
        
        # Read output in real-time
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                self.log_queue.put(output.strip())
                
                # Parse for progress information
                if 'Time =' in output:
                    self._parse_time_step(output)
                elif 'Solving for' in output:
                    self._parse_residual(output)
                    
        rc = process.poll()
        if rc != 0:
            raise Exception(f"Command failed with return code {rc}")
            
    def _parse_time_step(self, line):
        """Parse time step information"""
        match = re.search(r'Time = ([\d.]+)', line)
        if match:
            current_time = float(match.group(1))
            max_time = 10.0  # Simulation end time
            progress = (current_time / max_time) * 100
            self.progress_var.set(progress)
            self.progress_label.configure(text=f"Time: {current_time:.3f}s / {max_time}s")
            
    def _parse_residual(self, line):
        """Parse residual information for plotting"""
        # Example: "Solving for p_rgh, Initial residual = 0.001234"
        if 'Solving for' in line and 'Initial residual' in line:
            try:
                field_match = re.search(r'Solving for (\w+)', line)
                residual_match = re.search(r'Initial residual = ([\d.e-]+)', line)
                
                if field_match and residual_match:
                    field = field_match.group(1)
                    residual = float(residual_match.group(1))
                    
                    if field in self.residual_data:
                        self.residual_data[field].append(residual)
                        if len(self.residual_data['Time']) < len(self.residual_data[field]):
                            self.residual_data['Time'].append(len(self.residual_data[field]))
            except:
                pass
                
    def run_all_steps(self):
        """Run all simulation steps in sequence"""
        steps = ['cleanup', 'blockmesh', 'copy_stl', 'setfields', 'snappyhex', 'checkmesh', 'interfoam']
        
        def run_next_step(step_index):
            if step_index < len(steps):
                self.run_step(steps[step_index])
                # Schedule next step (with delay to allow current step to complete)
                self.root.after(1000, lambda: run_next_step(step_index + 1))
                
        run_next_step(0)
        
    def stop_simulation(self):
        """Stop the current simulation"""
        if self.simulation_process:
            self.simulation_process.terminate()
            self.log_message("Simulation stopped by user")
            self.is_running = False
            
    def log_message(self, message):
        """Add message to log queue"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_queue.put(f"[{timestamp}] {message}")
        
    def update_log_display(self):
        """Update log display from queue"""
        try:
            while True:
                message = self.log_queue.get_nowait()
                self.log_text.insert(tk.END, message + "\n")
                
                if self.autoscroll:
                    self.log_text.see(tk.END)
                    
        except queue.Empty:
            pass
            
        # Schedule next update
        self.root.after(100, self.update_log_display)
        
    def clear_log(self):
        """Clear the log display"""
        self.log_text.delete(1.0, tk.END)
        
    def save_log(self):
        """Save log to file"""
        filename = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        if filename:
            with open(filename, 'w') as f:
                f.write(self.log_text.get(1.0, tk.END))
                
    def toggle_autoscroll(self):
        """Toggle autoscroll for log"""
        self.autoscroll = not self.autoscroll
        
    def plot_residuals(self):
        """Plot residual convergence"""
        self.residuals_fig.clear()
        
        if not any(self.residual_data['Time']):
            ax = self.residuals_fig.add_subplot(111)
            ax.text(0.5, 0.5, 'No residual data available\nRun simulation to see convergence',
                   ha='center', va='center', transform=ax.transAxes)
            self.residuals_canvas.draw()
            return
            
        ax = self.residuals_fig.add_subplot(111)
        
        colors = ['blue', 'red', 'green', 'orange']
        fields = ['p_rgh', 'Ux', 'Uy', 'Uz']
        
        for i, field in enumerate(fields):
            if field in self.residual_data and self.residual_data[field]:
                ax.semilogy(self.residual_data['Time'][:len(self.residual_data[field])], 
                           self.residual_data[field], 
                           color=colors[i], label=field, marker='o', markersize=3)
                           
        ax.set_xlabel('Iteration')
        ax.set_ylabel('Residual')
        ax.set_title('Convergence History')
        ax.legend()
        ax.grid(True)
        
        self.residuals_canvas.draw()
        
    def update_residuals_plot(self):
        """Update residuals plot"""
        self.plot_residuals()
        
    def save_residuals_plot(self):
        """Save residuals plot to file"""
        filename = filedialog.asksaveasfilename(
            defaultextension=".png",
            filetypes=[("PNG files", "*.png"), ("PDF files", "*.pdf"), ("All files", "*.*")]
        )
        if filename:
            self.residuals_fig.savefig(filename, dpi=300, bbox_inches='tight')
            
    def view_case_info(self):
        """View case information"""
        if not self.current_case:
            messagebox.showwarning("Warning", "No case selected!")
            return
            
        info_file = f"cases/{self.current_case}/case_info.json"
        if os.path.exists(info_file):
            try:
                with open(info_file, 'r') as f:
                    info = json.load(f)
                    
                info_text = json.dumps(info, indent=2)
                
                # Show in popup window
                popup = tk.Toplevel(self.root)
                popup.title(f"Case Info: {self.current_case}")
                popup.geometry("600x400")
                
                text_widget = scrolledtext.ScrolledText(popup, wrap=tk.WORD)
                text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
                text_widget.insert(1.0, info_text)
                text_widget.configure(state='disabled')
                
            except Exception as e:
                messagebox.showerror("Error", f"Could not read case info: {str(e)}")
        else:
            messagebox.showinfo("Info", "No case info file found")
            
    def analyze_forces(self):
        """Analyze force data"""
        if not self.current_case:
            messagebox.showwarning("Warning", "No case selected!")
            return
            
        forces_file = f"cases/{self.current_case}/postProcessing/forces/0/forces.dat"
        if os.path.exists(forces_file):
            # Run force analysis script
            cmd = f"cd cases/{self.current_case} && python3 ../../utilities/python/coral_analysis.py"
            subprocess.run(cmd, shell=True)
            self.log_message("Force analysis completed")
        else:
            messagebox.showinfo("Info", "No force data available. Run simulation first.")
            
    def generate_report(self):
        """Generate simulation report"""
        self.log_message("Generating simulation report...")
        # This could run a comprehensive analysis script
        
    def open_paraview(self):
        """Open case in ParaView"""
        if not self.current_case:
            messagebox.showwarning("Warning", "No case selected!")
            return
            
        foam_file = f"cases/{self.current_case}/{self.current_case}.foam"
        if os.path.exists(foam_file):
            try:
                subprocess.Popen(['paraview', foam_file])
                self.log_message("Opening ParaView...")
            except FileNotFoundError:
                messagebox.showerror("Error", "ParaView not found in PATH")
        else:
            messagebox.showinfo("Info", "No .foam file found. Create one first.")

def main():
    root = tk.Tk()
    app = OpenFOAMMonitor(root)
    root.mainloop()

if __name__ == "__main__":
    main()